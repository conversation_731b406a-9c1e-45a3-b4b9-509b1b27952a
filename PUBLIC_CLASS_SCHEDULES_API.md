# Public Class Schedules API Documentation

## Overview

The Public Class Schedules API provides public access to view non-private class schedules without authentication. This API is designed for public-facing applications, websites, or mobile apps that need to display available class schedules to potential customers.

## Key Features

- ✅ **No Authentication Required** - Public access without login
- ✅ **Private Schedule Filtering** - Only returns schedules where `is_private = false` or `null`
- ✅ **Tenant Isolation** - Proper tenant-based filtering for multi-tenant architecture
- ✅ **Full Filtering Support** - Search, date range, location, facility, and instructor filters
- ✅ **Pagination** - Efficient pagination with limit/offset
- ✅ **Error Handling** - Comprehensive error handling and validation
- ✅ **Consistent API Pattern** - Follows existing public API patterns in the codebase

## Endpoints

### 1. List Public Class Schedules

**GET** `/api/public/class-schedules`

Returns a paginated list of public class schedules with optional filtering.

#### Required Parameters
- `tenantId` (string) - The tenant ID to filter schedules

#### Optional Parameters
- `classId` (string) - Filter by specific class
- `locationId` (string) - Filter by specific location
- `facilityId` (string) - Filter by specific facility
- `staffId` (string) - Filter by specific instructor/staff
- `startDate` (string) - Filter schedules from this date (YYYY-MM-DD)
- `endDate` (string) - Filter schedules until this date (YYYY-MM-DD)
- `search` (string) - Search in class names or instructor names
- `limit` (string) - Number of results per page (default: 20, max: 100)
- `offset` (string) - Number of results to skip (default: 0)

#### Example Request
```bash
GET /api/public/class-schedules?tenantId=1&limit=10&search=yoga&startDate=2024-01-15
```

#### Example Response
```json
{
  "success": true,
  "data": {
    "schedules": [
      {
        "id": "schedule_123",
        "class_id": "class_456",
        "tenant_id": 1,
        "location_id": "location_789",
        "facility_id": "facility_101",
        "staff_id": "staff_112",
        "start_date": "2024-01-15",
        "end_date": "2024-01-15",
        "start_time": "2024-01-15T10:00:00.000Z",
        "end_time": "2024-01-15T11:00:00.000Z",
        "duration": 60,
        "calender_color": "#3b82f6",
        "repeat_rule": "weekly",
        "pax": 20,
        "waitlist": 5,
        "allow_classpass": true,
        "is_private": false,
        "publish_now": true,
        "created_at": "2024-01-10T08:00:00.000Z",
        "updated_at": "2024-01-10T08:00:00.000Z"
      }
    ],
    "total": 25,
    "hasMore": true
  }
}
```

### 2. Get Individual Public Class Schedule

**GET** `/api/public/class-schedules/[id]`

Returns a single public class schedule by ID.

#### Required Parameters
- `id` (path parameter) - The schedule ID
- `tenantId` (query parameter) - The tenant ID for validation

#### Example Request
```bash
GET /api/public/class-schedules/schedule_123?tenantId=1
```

#### Example Response
```json
{
  "success": true,
  "data": {
    "id": "schedule_123",
    "class_id": "class_456",
    "tenant_id": 1,
    "location_id": "location_789",
    "facility_id": "facility_101",
    "staff_id": "staff_112",
    "start_date": "2024-01-15",
    "end_date": "2024-01-15",
    "start_time": "2024-01-15T10:00:00.000Z",
    "end_time": "2024-01-15T11:00:00.000Z",
    "duration": 60,
    "calender_color": "#3b82f6",
    "repeat_rule": "weekly",
    "pax": 20,
    "waitlist": 5,
    "allow_classpass": true,
    "is_private": false,
    "publish_now": true,
    "created_at": "2024-01-10T08:00:00.000Z",
    "updated_at": "2024-01-10T08:00:00.000Z",
    "class_name": "Morning Yoga",
    "location_name": "Main Studio",
    "facility_name": "Studio A",
    "instructor_name": "John Doe"
  }
}
```

## Security & Privacy

### Private Schedule Protection
- Schedules with `is_private = true` are **never** returned by the public API
- Only schedules with `is_private = false` or `is_private = null` are accessible
- This ensures private/internal schedules remain protected

### Tenant Isolation
- All requests require a valid `tenantId` parameter
- Schedules are filtered by tenant to ensure proper data isolation
- Cross-tenant data access is prevented

### Rate Limiting
- Public API has a maximum limit of 100 results per request
- This prevents abuse and ensures reasonable resource usage

## Error Handling

### Common Error Responses

#### Missing Tenant ID (400)
```json
{
  "success": false,
  "error": "Invalid parameters",
  "details": [
    {
      "code": "custom",
      "message": "Invalid tenant ID",
      "path": ["tenantId"]
    }
  ]
}
```

#### Schedule Not Found (404)
```json
{
  "success": false,
  "error": "Class schedule not found or not available for public access"
}
```

#### Server Error (500)
```json
{
  "success": false,
  "error": "Failed to fetch class schedules"
}
```

## Testing

### Test Endpoint
A test endpoint is available to verify the public API functionality:

**GET** `/api/test-public-class-schedules`

This endpoint tests:
- List endpoint functionality
- Individual endpoint functionality  
- Private schedule filtering
- Error handling

### Manual Testing Examples

```bash
# Test list endpoint
curl "http://localhost:3000/api/public/class-schedules?tenantId=1&limit=5"

# Test individual endpoint (replace with actual schedule ID)
curl "http://localhost:3000/api/public/class-schedules/schedule_123?tenantId=1"

# Test error handling
curl "http://localhost:3000/api/public/class-schedules"

# Test filtering
curl "http://localhost:3000/api/public/class-schedules?tenantId=1&search=yoga&startDate=2024-01-15"
```

## Implementation Details

### Service Layer
- Added `searchPublicSchedules()` method to `ClassScheduleService`
- Added `getPublicById()` method to `ClassScheduleService`
- Both methods include proper filtering for `is_private` field

### Database Filtering
The private schedule filtering is implemented at the database level:
```sql
WHERE (is_private = false OR is_private IS NULL)
```

### Middleware Configuration
The public endpoints are added to the middleware public routes list:
- `/api/public/class-schedules`
- `/api/test-public-class-schedules`

## Usage Examples

### Frontend Integration
```javascript
// Fetch public class schedules
const fetchPublicSchedules = async (tenantId, filters = {}) => {
  const params = new URLSearchParams({
    tenantId: tenantId.toString(),
    ...filters
  });
  
  const response = await fetch(`/api/public/class-schedules?${params}`);
  const data = await response.json();
  
  if (data.success) {
    return data.data;
  } else {
    throw new Error(data.error);
  }
};

// Usage
const schedules = await fetchPublicSchedules(1, {
  search: 'yoga',
  startDate: '2024-01-15',
  limit: '10'
});
```

### Mobile App Integration
```swift
// Swift example for iOS
func fetchPublicSchedules(tenantId: Int, completion: @escaping (Result<[Schedule], Error>) -> Void) {
    let url = URL(string: "https://yourapp.com/api/public/class-schedules?tenantId=\(tenantId)")!
    
    URLSession.shared.dataTask(with: url) { data, response, error in
        // Handle response
    }.resume()
}
```

## Migration Notes

### Existing API Unchanged
- The existing authenticated `/api/class-schedules` endpoint remains unchanged
- No breaking changes to existing functionality
- Private schedules are still accessible through authenticated endpoints

### Database Schema
- No database schema changes required
- Uses existing `is_private` field in `class_schedules` table
- Leverages existing indexes for optimal performance

## Support

For questions or issues with the Public Class Schedules API, please refer to:
- API documentation in this file
- Test endpoint results at `/api/test-public-class-schedules`
- Existing codebase patterns in `/api/public/` directory
