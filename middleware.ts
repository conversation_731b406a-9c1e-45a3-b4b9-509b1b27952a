import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";

/**
 * Middleware untuk Route Protection dengan RBAC
 * 
 * Middleware ini handle:
 * 1. Authentication check
 * 2. RBAC-based route protection
 * 3. Tenant isolation
 * 4. Location-based access control
 */

// Routes yang perlu protection
const protectedRoutes = [
  "/dashboard",
  "/admin",
  "/api/roles",
  "/api/permissions",
  "/api/location-access",
];

// Routes yang hanya bisa diakses super admin
const superAdminRoutes = [
  "/admin/system",
  "/admin/tenants",
  "/api/admin",
];

// Routes yang hanya bisa diakses tenant admin
const tenantAdminRoutes = [
  "/dashboard/users",
  "/dashboard/roles",
  "/dashboard/settings",
];

// Routes yang perlu location access
const locationRestrictedRoutes = [
  "/dashboard/classes",
  "/dashboard/equipment",
  "/dashboard/bookings",
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware untuk static files dan API routes yang tidak perlu protection
  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/api/auth") ||
    pathname.includes(".")
  ) {
    return NextResponse.next();
  }

  // Get session
  const session = await auth();

  // Check if route needs protection
  const needsProtection = protectedRoutes.some(route => pathname.startsWith(route));
  
  if (needsProtection && !session) {
    // Redirect ke login jika belum login
    const loginUrl = new URL("/auth/signin", request.url);
    loginUrl.searchParams.set("callbackUrl", pathname);
    return NextResponse.redirect(loginUrl);
  }

  if (session) {
    // Super Admin Route Protection
    const needsSuperAdmin = superAdminRoutes.some(route => pathname.startsWith(route));
    const userRoles = session.user?.roles || [];
    const isSuperAdmin = userRoles.includes("super_admin");

    if (needsSuperAdmin && !isSuperAdmin) {
      return NextResponse.redirect(new URL("/dashboard?error=insufficient_permissions", request.url));
    }

    // Tenant Admin Route Protection
    const needsTenantAdmin = tenantAdminRoutes.some(route => pathname.startsWith(route));
    const isTenantAdmin = userRoles.includes("tenant_admin") || isSuperAdmin;

    if (needsTenantAdmin && !isTenantAdmin) {
      return NextResponse.redirect(new URL("/dashboard?error=insufficient_permissions", request.url));
    }

    // Location-based Route Protection (simplified for edge runtime)
    const needsLocationAccess = locationRestrictedRoutes.some(route => pathname.startsWith(route));
    if (needsLocationAccess && !isSuperAdmin) {
      // For now, allow access if user has any location access
      // More complex location checks can be done in API routes
      const accessibleLocations = session.user?.accessibleLocations || [];
      if (accessibleLocations.length === 0) {
        return NextResponse.redirect(new URL("/dashboard?error=location_access_denied", request.url));
      }
    }

    // Add RBAC headers untuk API routes
    if (pathname.startsWith("/api/")) {
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set("x-user-id", session.user.id);
      requestHeaders.set("x-tenant-id", session.user.tenantId?.toString() || "");
      requestHeaders.set("x-user-roles", JSON.stringify(session.user.roles || []));
      requestHeaders.set("x-user-permissions", JSON.stringify(session.user.permissions || []));

      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      });
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api/auth|_next/static|_next/image|favicon.ico).*)",
  ],
};
