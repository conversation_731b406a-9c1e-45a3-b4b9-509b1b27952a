import { NextRequest, NextResponse } from "next/server";
import { ClassScheduleService } from "@/lib/services/class-schedule.service";
import { auth } from "@/lib/auth/config";
import { z } from "zod";

/**
 * API Route untuk Class Schedules
 * 
 * Endpoint ini handle CRUD operations untuk class schedules.
 * Mengiku<PERSON> pattern yang sama dengan API routes lain yang sudah ada.
 * 
 * Kenapa pakai pattern ini?
 * - Konsisten dengan API routes lain
 * - Mudah di-maintain dan di-extend
 * - Built-in validation dengan Zod
 * - Proper error handling
 * - Authentication dan authorization
 */

// Schema untuk validasi create class schedule
const createClassScheduleSchema = z.object({
  tenant_id: z.number().int().positive(),
  class_id: z.string().min(1, "Class ID is required"),
  location_id: z.string().optional(),
  facility_id: z.string().optional(),
  staff_id: z.string().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  start_time: z.string().optional(),
  end_time: z.string().optional(),
  duration: z.number().int().positive(),
  calender_color: z.string().optional(),
  repeat_rule: z.enum(["none", "daily", "weekly", "monthly"]).default("none"),
  pax: z.number().int().positive().default(1),
  waitlist: z.number().int().positive().default(1),
  allow_classpass: z.boolean().default(false),
  is_private: z.boolean().default(false),
  publish_now: z.boolean().default(false),
  publish_at: z.string().optional(),
  auto_cancel_if_minimum_not_met: z.boolean().default(false),
  booking_window_start: z.string().optional(),
  booking_window_end: z.string().optional(),
  check_in_window_start: z.string().optional(),
  check_in_window_end: z.string().optional(),
  late_cancellation_rule: z.string().optional(),
});

// Schema untuk validasi update class schedule
const updateClassScheduleSchema = createClassScheduleSchema.partial().omit({ tenant_id: true });

/**
 * GET /api/class-schedules
 * 
 * Ambil class schedules dengan berbagai filter dan pagination.
 * Support search, filter by class/location/facility/instructor, date range.
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse query parameters
  const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const classId = searchParams.get("classId");
    const locationId = searchParams.get("locationId");
    const facilityId = searchParams.get("facilityId");
    const staffId = searchParams.get("staffId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const search = searchParams.get("search");
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = parseInt(searchParams.get("offset") || "0");

    // Validate required parameters
    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const tenantIdNum = parseInt(tenantId);
    if (isNaN(tenantIdNum)) {
      return NextResponse.json(
        { error: "Invalid tenant ID" },
        { status: 400 }
      );
    }



    // Call service method
    const result = await ClassScheduleService.searchSchedules(
      tenantIdNum,
      classId || undefined,
      locationId || undefined,
      facilityId || undefined,
      staffId || undefined,
      startDate || undefined,
      endDate || undefined,
      search || undefined,
      limit,
      offset
    );



    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error fetching class schedules:", error);
    return NextResponse.json(
      { error: "Failed to fetch class schedules" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/class-schedules
 * 
 * Create class schedule baru.
 * Requires authentication dan valid data.
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse dan validate request body
    const body = await request.json();
    console.log('🔍 Request body received:', JSON.stringify(body, null, 2));
    const validatedData = createClassScheduleSchema.parse(body);

    // Convert datetime strings to Date objects
    const scheduleData = {
      ...validatedData,
      start_time: validatedData.start_time ? new Date(validatedData.start_time) : undefined,
      end_time: validatedData.end_time ? new Date(validatedData.end_time) : undefined,
      publish_at: validatedData.publish_at ? new Date(validatedData.publish_at) : undefined,
      booking_window_start: validatedData.booking_window_start ? new Date(validatedData.booking_window_start) : undefined,
      booking_window_end: validatedData.booking_window_end ? new Date(validatedData.booking_window_end) : undefined,
      check_in_window_start: validatedData.check_in_window_start ? new Date(validatedData.check_in_window_start) : undefined,
      check_in_window_end: validatedData.check_in_window_end ? new Date(validatedData.check_in_window_end) : undefined,
    };

    // Create class schedule
    const newSchedule = await ClassScheduleService.create(scheduleData);

    return NextResponse.json({
      success: true,
      data: newSchedule,
      message: "Class schedule created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating class schedule:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    // Handle specific business logic errors
    if (error instanceof Error) {
      // Foreign key validation errors
      if (error.message.includes("not found")) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }

      // Schedule conflict errors
      if (error.message.includes("conflict")) {
        return NextResponse.json(
          {
            error: "Schedule Conflict",
            message: error.message,
            type: "SCHEDULE_CONFLICT"
          },
          { status: 409 } // 409 Conflict status code
        );
      }

      // Other validation errors
      if (error.message.includes("validation") || error.message.includes("invalid")) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: "Failed to create class schedule" },
      { status: 500 }
    );
  }
}
