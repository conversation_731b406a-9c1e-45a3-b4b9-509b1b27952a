import { NextRequest, NextResponse } from "next/server";
import { ClassScheduleService } from "@/lib/services/class-schedule.service";
import { auth } from "@/lib/auth/config";
import { z } from "zod";

/**
 * API Route untuk Individual Class Schedule Operations
 * 
 * Handle GET, PUT, DELETE untuk single class schedule berdasarkan ID.
 * Mengikuti pattern yang sama dengan API routes lain.
 */

// Schema untuk validasi update
const updateClassScheduleSchema = z.object({
  class_id: z.string().optional(),
  location_id: z.string().optional(),
  facility_id: z.string().optional(),
  staff_id: z.string().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  start_time: z.string().optional(),
  end_time: z.string().optional(),
  duration: z.number().int().positive().optional(),
  calender_color: z.string().optional(),
  repeat_rule: z.enum(["none", "daily", "weekly", "monthly"]).optional(),
  pax: z.number().int().positive().optional(),
  waitlist: z.number().int().positive().optional(),
  allow_classpass: z.boolean().optional(),
  is_private: z.boolean().optional(),
  publish_now: z.boolean().optional(),
  publish_at: z.string().optional(),
  auto_cancel_if_minimum_not_met: z.boolean().optional(),
  booking_window_start: z.string().optional(),
  booking_window_end: z.string().optional(),
  check_in_window_start: z.string().optional(),
  check_in_window_end: z.string().optional(),
  late_cancellation_rule: z.string().optional(),
});

/**
 * GET /api/class-schedules/[id]
 * 
 * Ambil single class schedule berdasarkan ID dengan relasi.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: "Schedule ID is required" },
        { status: 400 }
      );
    }

    // Get class schedule dengan relasi
    const schedule = await ClassScheduleService.getById(id);

    if (!schedule) {
      return NextResponse.json(
        { error: "Class schedule not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: schedule,
    });
  } catch (error) {
    console.error("Error fetching class schedule:", error);
    return NextResponse.json(
      { error: "Failed to fetch class schedule" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/class-schedules/[id]
 * 
 * Update class schedule berdasarkan ID.
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: "Schedule ID is required" },
        { status: 400 }
      );
    }

    // Parse dan validate request body
    const body = await request.json();
    const validatedData = updateClassScheduleSchema.parse(body);

    // Convert datetime strings to Date objects
    const updateData = {
      ...validatedData,
      start_time: validatedData.start_time ? new Date(validatedData.start_time) : undefined,
      end_time: validatedData.end_time ? new Date(validatedData.end_time) : undefined,
      publish_at: validatedData.publish_at ? new Date(validatedData.publish_at) : undefined,
      booking_window_start: validatedData.booking_window_start ? new Date(validatedData.booking_window_start) : undefined,
      booking_window_end: validatedData.booking_window_end ? new Date(validatedData.booking_window_end) : undefined,
      check_in_window_start: validatedData.check_in_window_start ? new Date(validatedData.check_in_window_start) : undefined,
      check_in_window_end: validatedData.check_in_window_end ? new Date(validatedData.check_in_window_end) : undefined,
    };

    // Update class schedule
    const updatedSchedule = await ClassScheduleService.update(id, updateData);

    if (!updatedSchedule) {
      return NextResponse.json(
        { error: "Class schedule not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedSchedule,
      message: "Class schedule updated successfully",
    });
  } catch (error) {
    console.error("Error updating class schedule:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Validation failed",
          details: error.errors
        },
        { status: 400 }
      );
    }

    // Handle specific business logic errors
    if (error instanceof Error) {
      // Schedule conflict errors
      if (error.message.includes("conflict")) {
        return NextResponse.json(
          {
            error: "Schedule Conflict",
            message: error.message,
            type: "SCHEDULE_CONFLICT"
          },
          { status: 409 } // 409 Conflict status code
        );
      }

      // Foreign key validation errors
      if (error.message.includes("not found")) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }

      // Other validation errors
      if (error.message.includes("validation") || error.message.includes("invalid")) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: "Failed to update class schedule" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/class-schedules/[id]
 * 
 * Delete class schedule berdasarkan ID.
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: "Schedule ID is required" },
        { status: 400 }
      );
    }

    // Delete class schedule
    const deletedSchedule = await ClassScheduleService.delete(id);

    return NextResponse.json({
      success: true,
      data: deletedSchedule,
      message: "Class schedule deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting class schedule:", error);

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Failed to delete class schedule" },
      { status: 500 }
    );
  }
}
