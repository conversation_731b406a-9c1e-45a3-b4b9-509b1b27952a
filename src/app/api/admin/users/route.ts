import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { db } from "@/lib/db";
import { users, organizations, organizationMembers } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";
import { z } from "zod";
import { createId } from "@paralleldrive/cuid2";
import { RBACHelpers } from "@/lib/auth/rbac-integration";

// ✅ GET /api/admin/users - Get all users (admin only)
export async function GET(request: NextRequest) {
  console.log("🚀 Admin users endpoint called!");
  try {
    const session = await auth();
    console.log("🔍 Admin users endpoint - session:", JSON.stringify(session, null, 2));
    if (!session?.user) {
      console.log("❌ Admin users endpoint - no session or user");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only super admins and tenant admins can list all users
    console.log("🔍 Admin users endpoint - user role:", session.user.role);
    console.log("🔍 Admin users endpoint - user roles:", session.user.roles);
    console.log("🔍 Admin users endpoint - user permissions:", session.user.permissions);

    const isSuperAdmin = RBACHelpers.isSuperAdmin(session);
    const isTenantAdmin = RBACHelpers.isTenantAdmin(session);

    if (!isSuperAdmin && !isTenantAdmin) {
      console.log("🔍 Admin users endpoint - role check failed, user is not super admin or tenant admin");
      return NextResponse.json({ error: "Forbidden - Admin access required" }, { status: 403 });
    }

    console.log("✅ Admin users endpoint - role check passed, fetching users...");

    // Get all users with their organization info
    const allUsers = await db
        .select({
          id: users.id,
          email: users.email,
          name: users.name,
          image: users.image,
          role: users.role,
          credits: users.credits,
          isActive: users.isActive,
          emailVerified: users.emailVerified,
          createdAt: users.createdAt,
          organizationId: users.organizationId,
          organizationName: organizations.name,
        })
        .from(users)
        .leftJoin(organizations, eq(users.organizationId, organizations.id))
        .orderBy(desc(users.createdAt));

    console.log("✅ Successfully fetched", allUsers.length, "users");
    return NextResponse.json(allUsers);
  } catch (error) {
    console.error("💥 Error fetching users:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Schema for create user validation
const createUserSchema = z.object({
  email: z.string().email("Invalid email format"),
  name: z.string().min(1, "Name is required"),
  role: z.string().min(1, "Role is required"),
  image: z.string().optional(),
  organizationId: z.string().optional(),
  tenantId: z.number().optional(),
});

// ✅ POST /api/admin/users - Create new user (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only super admins and tenant admins can create users
    const isSuperAdmin = RBACHelpers.isSuperAdmin(session);
    const isTenantAdmin = RBACHelpers.isTenantAdmin(session);

    if (!isSuperAdmin && !isTenantAdmin) {
      return NextResponse.json({ error: "Forbidden - Admin access required" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createUserSchema.parse(body);

      // Check if email already exists
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.email, validatedData.email))
        .limit(1);

      if (existingUser.length > 0) {
        return NextResponse.json(
          { error: "User with this email already exists" },
          { status: 409 }
        );
      }

      // Create new user
      const newUser = {
        id: createId(),
        email: validatedData.email,
        name: validatedData.name,
        role: validatedData.role,
        image: validatedData.image || null,
        organizationId: validatedData.organizationId || null,
        tenantId: validatedData.tenantId || null,
        credits: 0,
        isActive: true,
        emailVerified: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const [createdUser] = await db
        .insert(users)
        .values(newUser)
        .returning();

      return NextResponse.json(createdUser, { status: 201 });
    } catch (error) {
      console.error("Error creating user:", error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation failed", details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      );
    }
}
