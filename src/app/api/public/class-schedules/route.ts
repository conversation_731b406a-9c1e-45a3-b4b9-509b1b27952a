import { NextRequest, NextResponse } from "next/server";
import { ClassScheduleService } from "@/lib/services/class-schedule.service";
import { z } from "zod";

/**
 * Public Class Schedules API Route
 * 
 * Public endpoint untuk mengambil class schedules yang tidak private.
 * Tidak memerlukan authentication, hanya menampilkan schedules yang public.
 */

// Schema untuk validasi query parameters
const querySchema = z.object({
  tenantId: z.string().transform((val) => {
    const parsed = parseInt(val, 10);
    if (isNaN(parsed) || parsed <= 0) {
      throw new Error("Invalid tenant ID");
    }
    return parsed;
  }),
  classId: z.string().optional(),
  locationId: z.string().optional(),
  facilityId: z.string().optional(),
  staffId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
  limit: z.string().transform((val) => {
    const parsed = parseInt(val, 10);
    return isNaN(parsed) ? 20 : Math.min(Math.max(parsed, 1), 100); // Max 100 untuk public API
  }).optional(),
  offset: z.string().transform((val) => {
    const parsed = parseInt(val, 10);
    return isNaN(parsed) ? 0 : Math.max(parsed, 0);
  }).optional(),
});

/**
 * GET /api/public/class-schedules
 * 
 * Public endpoint untuk mengambil class schedules yang tidak private.
 * Hanya menampilkan schedules dengan is_private = false atau null.
 * Memerlukan tenantId untuk tenant isolation.
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Convert URLSearchParams to plain object untuk validation
    const params: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      params[key] = value;
    });

    // Validate query parameters
    const validatedParams = querySchema.parse(params);

    // Call service method untuk get public schedules
    const result = await ClassScheduleService.searchPublicSchedules(
      validatedParams.tenantId,
      validatedParams.classId,
      validatedParams.locationId,
      validatedParams.facilityId,
      validatedParams.staffId,
      validatedParams.startDate,
      validatedParams.endDate,
      validatedParams.search,
      validatedParams.limit || 20,
      validatedParams.offset || 0
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error fetching public class schedules:", error);
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Invalid parameters", 
          details: error.errors,
          success: false 
        },
        { status: 400 }
      );
    }

    // Handle other errors
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to fetch class schedules",
        success: false 
      },
      { status: 500 }
    );
  }
}
