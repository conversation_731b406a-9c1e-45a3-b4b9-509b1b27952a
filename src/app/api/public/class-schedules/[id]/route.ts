import { NextRequest, NextResponse } from "next/server";
import { ClassScheduleService } from "@/lib/services/class-schedule.service";
import { z } from "zod";

/**
 * Public Individual Class Schedule API Route
 * 
 * Public endpoint untuk mengambil single class schedule yang tidak private.
 * Tidak memerlukan authentication, hanya menampilkan schedule yang public.
 */

// Schema untuk validasi parameters
const paramsSchema = z.object({
  id: z.string().min(1, "Schedule ID is required"),
});

const querySchema = z.object({
  tenantId: z.string().transform((val) => {
    const parsed = parseInt(val, 10);
    if (isNaN(parsed) || parsed <= 0) {
      throw new Error("Invalid tenant ID");
    }
    return parsed;
  }),
});

/**
 * GET /api/public/class-schedules/[id]
 * 
 * Public endpoint untuk mengambil single class schedule berdasarkan ID.
 * Hanya menampilkan schedule dengan is_private = false atau null.
 * Memerlukan tenantId untuk tenant isolation.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate parameters
    const validatedParams = paramsSchema.parse(params);
    
    // Validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      queryParams[key] = value;
    });
    
    const validatedQuery = querySchema.parse(queryParams);

    // Get class schedule dengan validation untuk public access
    const schedule = await ClassScheduleService.getPublicById(
      validatedParams.id,
      validatedQuery.tenantId
    );

    if (!schedule) {
      return NextResponse.json(
        { 
          error: "Class schedule not found or not available for public access",
          success: false 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: schedule,
    });
  } catch (error) {
    console.error("Error fetching public class schedule:", error);
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Invalid parameters", 
          details: error.errors,
          success: false 
        },
        { status: 400 }
      );
    }

    // Handle other errors
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to fetch class schedule",
        success: false 
      },
      { status: 500 }
    );
  }
}
