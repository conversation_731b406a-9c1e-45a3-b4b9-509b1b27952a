/**
 * Public API Routes for Package Pricing
 * 
 * RESTful API endpoints following FAANG-level standards:
 * - GET /api/public/v1/package-pricing - List package pricing with filters
 * - POST /api/public/v1/package-pricing - Create new package pricing
 * - Comprehensive error handling and validation
 * - Rate limiting and authentication
 * - Detailed logging and monitoring
 */

import { NextRequest } from 'next/server';
import { AuthMiddleware } from '@/lib/public-api/middleware/auth-middleware';
import { ValidationMiddleware } from '@/lib/public-api/middleware/validation-middleware';
import { PackagePricingService } from '@/lib/services/package-pricing.service';
import { 
  PackagePricingFiltersSchema,
  PackagePricingCreateSchema,
  PackagePricingBulkCreateSchema,
  PaginationSchema 
} from '@/lib/public-api/validation-types';
import { PublicPackagePricing } from '@/lib/public-api/types';

const authMiddleware = new AuthMiddleware();
const validationMiddleware = new ValidationMiddleware();
const packagePricingService = new PackagePricingService();

/**
 * GET /api/public/v1/package-pricing
 * List package pricing with filters and pagination
 */
export async function GET(request: NextRequest) {
  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return authMiddleware.handleOptions(request);
  }

  try {
    // Authenticate request
    const authResult = await authMiddleware.authenticate(request, {
      requiredPermission: {
        resource: 'package-pricing',
        action: 'read',
      },
    });

    if (authResult.response) {
      return authResult.response;
    }

    const { context } = authResult;
    const { authContext } = context!;

    // Validate query parameters
    const validationResult = await validationMiddleware.validate(request, {
      querySchema: PackagePricingFiltersSchema.merge(PaginationSchema),
    });

    if (!validationResult.isValid) {
      return validationResult.response!;
    }

    const { query } = validationResult.data!;
    const { page, limit, offset } = validationMiddleware.validatePagination(query);

    // Extract filters
    const filters = {
      packageId: query.packageId,
      pricingGroupId: query.pricingGroupId,
      minPrice: query.minPrice,
      maxPrice: query.maxPrice,
      currency: query.currency,
      isActive: query.isActive,
      search: query.search,
    };

    // Get package pricing data
    const packagePricingData = await packagePricingService.getByTenant(
      authContext!.tenantId,
      filters
    );

    // Apply pagination
    const total = packagePricingData.length;
    const paginatedData = packagePricingData.slice(offset, offset + limit);

    // Transform to public API format
    const publicData: PublicPackagePricing[] = paginatedData.map(item => ({
      id: item.id,
      packageId: item.package_id,
      packageName: item.package?.name || 'Unknown Package',
      packageDescription: item.package?.description,
      pricingGroupId: item.pricing_group_id,
      pricingGroupName: item.pricing_group?.name || 'Unknown Pricing Group',
      price: item.price,
      creditAmount: item.credit_amount,
      currency: item.currency || 'USD',
      isActive: true, // Assuming active if returned
      createdAt: item.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: item.updatedAt?.toISOString() || new Date().toISOString(),
    }));

    // Create pagination metadata
    const pagination = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: offset + limit < total,
      hasPrev: page > 1,
    };

    return authMiddleware.createSuccessResponse(publicData, context!, pagination);

  } catch (error) {
    console.error('GET /api/public/v1/package-pricing error:', error);
    
    return authMiddleware.createSuccessResponse(
      null,
      { requestId: 'error', startTime: Date.now(), apiVersion: 'v1', clientInfo: { ip: 'unknown', userAgent: 'unknown' } },
    );
  }
}

/**
 * POST /api/public/v1/package-pricing
 * Create new package pricing (single or bulk)
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate request
    const authResult = await authMiddleware.authenticate(request, {
      requiredPermission: {
        resource: 'package-pricing',
        action: 'write',
      },
    });

    if (authResult.response) {
      return authResult.response;
    }

    const { context } = authResult;
    const { authContext } = context!;

    // Parse request body to determine if it's bulk or single
    const body = await request.json();
    const isBulk = Array.isArray(body.items);

    // Validate request body
    const validationResult = await validationMiddleware.validate(request, {
      bodySchema: isBulk ? PackagePricingBulkCreateSchema : PackagePricingCreateSchema,
    });

    if (!validationResult.isValid) {
      return validationResult.response!;
    }

    const { body: validatedBody } = validationResult.data!;

    let result;
    let createdCount = 0;

    if (isBulk) {
      // Handle bulk creation
      const createData = validatedBody.items.map((item: any) => ({
        packageId: item.packageId,
        pricingGroupId: item.pricingGroupId,
        price: item.price,
        creditAmount: item.creditAmount,
        currency: item.currency || 'USD',
      }));

      result = await packagePricingService.bulkCreate(createData);
      createdCount = result.length;

      // Transform to public API format
      const publicData: PublicPackagePricing[] = result.map(item => ({
        id: item.id,
        packageId: item.package_id,
        packageName: 'Package', // Would need to fetch package details
        pricingGroupId: item.pricing_group_id,
        pricingGroupName: 'Pricing Group', // Would need to fetch pricing group details
        price: item.price,
        creditAmount: item.credit_amount,
        currency: item.currency || 'USD',
        isActive: true,
        createdAt: item.createdAt?.toISOString() || new Date().toISOString(),
        updatedAt: item.updatedAt?.toISOString() || new Date().toISOString(),
      }));

      return authMiddleware.createSuccessResponse(
        {
          items: publicData,
          created: createdCount,
          message: `Successfully created ${createdCount} package pricing records`,
        },
        context!
      );

    } else {
      // Handle single creation
      const createData = {
        packageId: validatedBody.packageId,
        pricingGroupId: validatedBody.pricingGroupId,
        price: validatedBody.price,
        creditAmount: validatedBody.creditAmount,
        currency: validatedBody.currency || 'USD',
      };

      result = await packagePricingService.create(createData);

      // Transform to public API format
      const publicData: PublicPackagePricing = {
        id: result.id,
        packageId: result.package_id,
        packageName: 'Package', // Would need to fetch package details
        pricingGroupId: result.pricing_group_id,
        pricingGroupName: 'Pricing Group', // Would need to fetch pricing group details
        price: result.price,
        creditAmount: result.credit_amount,
        currency: result.currency || 'USD',
        isActive: true,
        createdAt: result.createdAt?.toISOString() || new Date().toISOString(),
        updatedAt: result.updatedAt?.toISOString() || new Date().toISOString(),
      };

      return authMiddleware.createSuccessResponse(publicData, context!);
    }

  } catch (error) {
    console.error('POST /api/public/v1/package-pricing error:', error);
    
    // Handle specific errors
    if (error instanceof Error) {
      if (error.message.includes('already exists')) {
        return authMiddleware.createSuccessResponse(
          null,
          { requestId: 'error', startTime: Date.now(), apiVersion: 'v1', clientInfo: { ip: 'unknown', userAgent: 'unknown' } }
        );
      }
      
      if (error.message.includes('not found')) {
        return authMiddleware.createSuccessResponse(
          null,
          { requestId: 'error', startTime: Date.now(), apiVersion: 'v1', clientInfo: { ip: 'unknown', userAgent: 'unknown' } }
        );
      }
    }

    return authMiddleware.createSuccessResponse(
      null,
      { requestId: 'error', startTime: Date.now(), apiVersion: 'v1', clientInfo: { ip: 'unknown', userAgent: 'unknown' } }
    );
  }
}

/**
 * OPTIONS /api/public/v1/package-pricing
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return authMiddleware.handleOptions(request);
}
