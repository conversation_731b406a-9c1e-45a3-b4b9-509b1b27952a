"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Search, Edit, Trash2, Calendar, Filter, Clock, Users, MapPin, List, Grid3X3 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ClassScheduleForm } from "@/components/forms/class-schedule-form";
import {
  useClassScheduleSearchAdvanced,
  useCreateClassSchedule,
  useUpdateClassSchedule,
  useDeleteClassSchedule,
  useClassSchedule,
  type ClassScheduleFormData,
} from "@/lib/hooks/queries/use-class-schedule-queries";
import { useClassesByTenant } from "@/lib/hooks/queries/use-class-queries";
import { useLocation } from "@/lib/hooks/queries/use-location-queries";
import { type ClassSchedule } from "@/lib/db/schema";
import { SuccessToast } from "@/components/ui/success-toast";
import { ClassScheduleCalendar } from "./class-schedule-calendar";
import { formatDateTimeForDisplay } from "@/lib/utils/datetime";

/**
 * ClassSchedulesManagement Component
 * 
 * Component untuk manage class schedules dengan full CRUD functionality.
 * Mengikuti pattern yang sama dengan management components lain yang sudah ada.
 * 
 * Features:
 * - Search schedules by class name atau instructor
 * - Filter by class, location, date range
 * - Create, edit, delete schedules
 * - Responsive grid layout
 * - Success feedback dengan SuccessToast
 * - Calendar-like view dengan color coding
 */

interface ClassSchedulesManagementProps {
  tenantId: number;
}

export function ClassSchedulesManagement({ tenantId }: ClassSchedulesManagementProps) {
  // State management
  const [viewMode, setViewMode] = useState<"list" | "calendar">("list");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedClassId, setSelectedClassId] = useState<string>("all");
  const [selectedLocationId, setSelectedLocationId] = useState<string>("all");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<ClassSchedule | null>(null);
  const [editingScheduleId, setEditingScheduleId] = useState<string | null>(null);
  const [deletingSchedule, setDeletingSchedule] = useState<ClassSchedule | null>(null);

  // Calendar selection state
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedTime, setSelectedTime] = useState<string | undefined>(undefined);

  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [createdSchedule, setCreatedSchedule] = useState<ClassSchedule | null>(null);

  // Get classes and locations for filters
  const { data: classes = [] } = useClassesByTenant(tenantId);
  const { data: locations = [] } = useLocation(tenantId);

  // Queries
  const {
    data: searchResult,
    isLoading,
    error,
    refetch,
  } = useClassScheduleSearchAdvanced(
    tenantId,
    selectedClassId === "all" ? undefined : selectedClassId,
    selectedLocationId === "all" ? undefined : selectedLocationId,
    undefined, // facilityId
    undefined, // staffId
    startDate || undefined,
    endDate || undefined,
    searchTerm,
    50,
    0
  );

  // Fetch detailed schedule data for editing
  const { data: detailedScheduleData, isLoading: isLoadingDetailedSchedule } = useClassSchedule(editingScheduleId || "");

  // Mutations
  const createMutation = useCreateClassSchedule();
  const updateMutation = useUpdateClassSchedule();
  const deleteMutation = useDeleteClassSchedule();

  // Handlers
  const handleCreateSchedule = async (data: ClassScheduleFormData) => {
    try {
      const newSchedule = await createMutation.mutateAsync(data);
      setCreatedSchedule(newSchedule);
      setShowSuccessToast(true);
      setIsCreateDialogOpen(false);
      // Note: No need for manual refetch() - the enhanced mutation hook handles cache invalidation
    } catch (error) {
      console.error("Error creating schedule:", error);
      // Error will be handled by the form's conflict validation
    }
  };

  const handleEditSchedule = (schedule: ClassSchedule) => {
    setEditingSchedule(schedule);
    setEditingScheduleId(schedule.id);
  };

  const handleUpdateSchedule = async (data: ClassScheduleFormData) => {
    if (!editingSchedule) return;

    try {
      await updateMutation.mutateAsync({
        id: editingSchedule.id,
        data,
      });
      setEditingSchedule(null);
      setEditingScheduleId(null);
      refetch();
    } catch (error) {
      console.error("Error updating schedule:", error);
    }
  };

  const handleDeleteSchedule = async () => {
    if (!deletingSchedule) return;

    try {
      await deleteMutation.mutateAsync(deletingSchedule.id);
      setDeletingSchedule(null);
      refetch();
    } catch (error) {
      console.error("Error deleting schedule:", error);
    }
  };

  const schedules = searchResult?.schedules || [];

  // Helper function untuk format tanggal dan waktu (konsisten dengan calendar)
  const formatDateTime = (date: string | Date | null, time: string | Date | null) => {
    return formatDateTimeForDisplay(date, time);
  };

  // Helper function untuk get class name
  const getClassName = (classId: string) => {
    const classItem = classes.find(c => c.id === classId);
    return classItem?.name || "Unknown Class";
  };

  // Helper function untuk get location name
  const getLocationName = (locationId: string | null) => {
    if (!locationId) return "No Location";
    const location = locations.find(l => l.id === locationId);
    return location?.name || "Unknown Location";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Class Schedules</h1>
          <p className="text-muted-foreground">
            Manage class schedules and timetables
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* View Toggle */}
          <div className="flex items-center border rounded-lg p-1">
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
              className="h-8"
            >
              <List className="h-4 w-4 mr-2" />
              List
            </Button>
            <Button
              variant={viewMode === "calendar" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("calendar")}
              className="h-8"
            >
              <Grid3X3 className="h-4 w-4 mr-2" />
              Calendar
            </Button>
          </div>

          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Schedule
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Class Schedule</DialogTitle>
              </DialogHeader>
              <ClassScheduleForm
                tenantId={tenantId}
                selectedDate={selectedDate} // Date selected from calendar
                selectedTime={selectedTime} // Time selected from calendar
                onSubmit={handleCreateSchedule}
                onCancel={() => {
                  setIsCreateDialogOpen(false);
                  setSelectedDate(undefined);
                  setSelectedTime(undefined);
                }}
                isLoading={createMutation.isPending}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by class or instructor..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Class Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Class</label>
              <Select value={selectedClassId} onValueChange={setSelectedClassId}>
                <SelectTrigger>
                  <SelectValue placeholder="All classes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {classes.map((classItem) => (
                    <SelectItem key={classItem.id} value={classItem.id}>
                      {classItem.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Location Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Location</label>
              
            </div>

            {/* Start Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>

            {/* End Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content Area */}
      {viewMode === "calendar" ? (
        <ClassScheduleCalendar
          tenantId={tenantId}
          selectedClassId={selectedClassId === "all" ? undefined : selectedClassId}
          selectedLocationId={selectedLocationId === "all" ? undefined : selectedLocationId}
          selectedInstructorId={undefined}
          onEventClick={(event) => {
            // Handle event click from calendar
            console.log("Event clicked:", event);
          }}
          onEventCreate={(date, time) => {
            // Handle create from calendar - capture selected date and time
            setSelectedDate(date);
            setSelectedTime(time);
            setIsCreateDialogOpen(true);
          }}
        />
      ) : (
        <div className="space-y-4">
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/2 mb-4"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-muted rounded w-full"></div>
                    <div className="h-3 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-red-500">Error loading schedules: {error.message}</p>
              <Button onClick={() => refetch()} className="mt-2">
                Try Again
              </Button>
            </CardContent>
          </Card>
        ) : schedules.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No schedules found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || selectedClassId !== "all" || selectedLocationId !== "all" || startDate || endDate
                  ? "No schedules match your current filters."
                  : "Get started by creating your first class schedule."}
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Schedule
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <AnimatePresence>
              {schedules.map((schedule) => (
                <motion.div
                  key={schedule.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      {/* Schedule Header */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg mb-1">
                            {getClassName(schedule.class_id)}
                          </h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <MapPin className="h-4 w-4" />
                            {getLocationName(schedule.location_id)}
                          </div>
                        </div>
                        {schedule.calender_color && (
                          <div
                            className="w-4 h-4 rounded-full border-2 border-white shadow-sm"
                            style={{ backgroundColor: schedule.calender_color }}
                          />
                        )}
                      </div>

                      {/* Schedule Details */}
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center gap-2 text-sm">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {schedule.start_date ? new Date(schedule.start_date).toLocaleDateString() : "No date"}
                          </span>
                        </div>

                        <div className="flex items-center gap-2 text-sm">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {formatDateTime(schedule.start_date, schedule.start_time)} - {" "}
                            {schedule.end_time ? new Date(schedule.end_time).toLocaleTimeString('en-US', {
                              hour: '2-digit',
                              minute: '2-digit',
                              hour12: false
                            }) : "No end time"}
                          </span>
                        </div>

                        <div className="flex items-center gap-2 text-sm">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span>Capacity: {schedule.pax} | Waitlist: {schedule.waitlist}</span>
                        </div>
                      </div>

                      {/* Badges */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {schedule.repeat_rule && schedule.repeat_rule !== "none" && (
                          <Badge variant="secondary" className="text-xs">
                            {schedule.repeat_rule}
                          </Badge>
                        )}
                        {schedule.is_private && (
                          <Badge variant="outline" className="text-xs">
                            Private
                          </Badge>
                        )}
                        {schedule.allow_classpass && (
                          <Badge variant="outline" className="text-xs">
                            Class Pass
                          </Badge>
                        )}
                        {schedule.publish_now && (
                          <Badge variant="default" className="text-xs">
                            Published
                          </Badge>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditSchedule(schedule)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setDeletingSchedule(schedule)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={!!editingSchedule} onOpenChange={(open) => {
        if (!open) {
          setEditingSchedule(null);
          setEditingScheduleId(null);
        }
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Class Schedule</DialogTitle>
          </DialogHeader>
          {editingSchedule && !isLoadingDetailedSchedule && (
            <ClassScheduleForm
              tenantId={tenantId}
              initialData={detailedScheduleData || editingSchedule}
              onSubmit={handleUpdateSchedule}
              onCancel={() => {
                setEditingSchedule(null);
                setEditingScheduleId(null);
              }}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingSchedule} onOpenChange={(open) => {
        if (!open) setDeletingSchedule(null);
      }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Class Schedule</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this class schedule for "{deletingSchedule && getClassName(deletingSchedule.class_id)}"?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSchedule}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Toast */}
      <SuccessToast
        open={showSuccessToast}
        onOpenChange={setShowSuccessToast}
        title="Schedule Created!"
        description={`Class schedule for "${createdSchedule && getClassName(createdSchedule.class_id)}" has been created successfully.`}
      />
    </div>
  );
}
