"use client";

import { useEffect, useState } from "react";
import { format } from "date-fns";
import { MoreHorizontal, UserPlus, Search, Filter, X } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ComponentLoading } from "@/components/ui/loading";
import { formatCredits } from "@/lib/stripe/config";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useRoles } from "@/lib/hooks/queries/use-role-queries";
import { useSession } from "next-auth/react";

interface User {
  id: string;
  email: string;
  name?: string;
  image?: string;
  role: string; // ✅ Changed: Support dynamic roles instead of hard-coded
  roleName?: string; // ✅ Added: Display name for role
  credits: number;
  isActive: boolean;
  emailVerified?: Date;
  createdAt: Date;
  organizationId?: string;
  organizationName?: string;
  tenantId?: number; // ✅ Added: For tenant isolation
}

export function UserManagement() {
  console.log("🚀 UserManagement component rendered!");
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all"); // ✅ Changed: Support dynamic role filtering
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newUserData, setNewUserData] = useState({
    email: "",
    name: "",
    role: "",
  });

  // ✅ Added: Fetch roles from Role Management system
  const { data: session } = useSession();
  const { data: rolesData, isLoading: isLoadingRoles } = useRoles();
  const roles = rolesData?.roles || [];

  useEffect(() => {
    console.log("🔍 UserManagement component mounted, calling fetchUsers...");
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      console.log("🔍 Fetching users from /api/admin/users...");
      const response = await fetch("/api/admin/users");
      console.log("📡 Response status:", response.status);

      if (response.ok) {
        const data = await response.json();
        console.log("✅ Users data received:", data);
        setUsers(data.map((user: any) => ({
          ...user,
          createdAt: new Date(user.createdAt),
          emailVerified: user.emailVerified ? new Date(user.emailVerified) : undefined,
        })));
      } else {
        const errorText = await response.text();
        console.error("❌ Failed to fetch users:", response.status, response.statusText, errorText);
      }
    } catch (error) {
      console.error("💥 Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserAction = async (userId: string, action: string) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ action }),
      });

      if (response.ok) {
        await fetchUsers(); // Refresh the list
      }
    } catch (error) {
      console.error("Failed to perform user action:", error);
    }
  };

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.organizationName?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = roleFilter === "all" || user.role === roleFilter;

    return matchesSearch && matchesRole;
  });

  // ✅ Added: Create user handler
  const handleCreateUser = async () => {
    try {
      const response = await fetch("/api/admin/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newUserData),
      });

      if (response.ok) {
        await fetchUsers(); // Refresh the list
        setIsCreateDialogOpen(false);
        setNewUserData({ email: "", name: "", role: "" }); // Reset form
      }
    } catch (error) {
      console.error("Failed to create user:", error);
    }
  };

  const getInitials = (name?: string) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (loading) {
    return <ComponentLoading text="Loading users..." />;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>User Management</CardTitle>
            <CardDescription>
              Manage users, their roles, and account status
            </CardDescription>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create New User</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={newUserData.email}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    placeholder="John Doe"
                    value={newUserData.name}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role">Role bos *</Label>
                  <Select
                    value={newUserData.role}
                    onValueChange={(value) => setNewUserData(prev => ({ ...prev, role: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      {/* ✅ Dynamic role options from Role Management */}
                      {roles.map((role: any) => (
                        <SelectItem key={role.id} value={role.name}>
                          {role.display_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateUser}
                    disabled={!newUserData.email || !newUserData.role || isLoadingRoles}
                  >
                    Create User
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Role: {roleFilter === "all" ? "All" :
                  roles.find(r => r.name === roleFilter)?.display_name || roleFilter}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setRoleFilter("all")}>
                All Roles
              </DropdownMenuItem>
              {/* ✅ Dynamic role options from Role Management */}
              {roles.map((role) => (
                <DropdownMenuItem
                  key={role.id}
                  onClick={() => setRoleFilter(role.name)}
                >
                  {role.display_name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Credits</TableHead>
              <TableHead>Organization</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Joined</TableHead>
              <TableHead className="w-[70px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.image || ""} alt={user.name || ""} />
                      <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{user.name || "Unnamed User"}</div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={user.role === "super_admin" || user.role === "tenant_admin" ? "default" : "secondary"}>
                    {user.roleName || roles.find((r: any) => r.name === user.role)?.display_name || user.role}
                  </Badge>
                </TableCell>
                <TableCell>
                  <span className="font-medium">{formatCredits(user.credits)}</span>
                </TableCell>
                <TableCell>
                  {user.organizationName ? (
                    <span className="text-sm">{user.organizationName}</span>
                  ) : (
                    <span className="text-sm text-muted-foreground">No organization</span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex flex-col space-y-1">
                    <Badge variant={user.isActive ? "default" : "destructive"}>
                      {user.isActive ? "Active" : "Inactive"}
                    </Badge>
                    {user.emailVerified ? (
                      <Badge variant="secondary" className="text-xs">
                        Verified
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-xs">
                        Unverified
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {format(user.createdAt, "MMM dd, yyyy")}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem
                        onClick={() => handleUserAction(user.id, user.isActive ? "deactivate" : "activate")}
                      >
                        {user.isActive ? "Deactivate" : "Activate"}
                      </DropdownMenuItem>
                      <DropdownMenuLabel>Change Role</DropdownMenuLabel>
                      {/* ✅ Dynamic role change options */}
                      {roles.filter((role: any) => role.name !== user.role).map((role: any) => (
                        <DropdownMenuItem
                          key={role.id}
                          onClick={() => handleUserAction(user.id, `change_role:${role.name}`)}
                        >
                          Make {role.display_name}
                        </DropdownMenuItem>
                      ))}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleUserAction(user.id, "reset_password")}
                      >
                        Reset Password
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleUserAction(user.id, "send_verification")}
                      >
                        Send Verification
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {filteredUsers.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No users found matching your criteria
          </div>
        )}
      </CardContent>
    </Card>
  );
}
