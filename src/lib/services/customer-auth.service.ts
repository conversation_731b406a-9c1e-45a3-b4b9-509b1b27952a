import { customerOAuthAccounts, customers, db } from "@/lib/db";
import { eq, and } from "drizzle-orm";
import { hash, compare } from "bcryptjs";
import { createId } from "@paralleldrive/cuid2";
import { tenantContextService } from "./tenant-context.service";
import { z } from "zod";

/**
 * Enterprise Customer Authentication Service
 * 
 * Handles customer authentication, OAuth integration, and tenant-scoped operations.
 * Implements FAANG-level security practices and audit logging.
 */

// Validation schemas
export const customerSignupSchema = z.object({
  email: z.string().email("Invalid email address"),
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(1, "Last name is required"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain uppercase, lowercase, and number")
    .optional(),
  phone: z.object({
    countryCode: z.string().optional(),
    number: z.string().optional(),
  }).optional(),
  dateOfBirth: z.string().optional(),
  termsAccepted: z.boolean().refine(val => val === true, "You must accept the terms"),
  marketingEmails: z.boolean().default(false),
  preferences: z.object({
    language: z.string().default("en"),
    timezone: z.string().optional(),
  }).optional(),
});

export const customerLoginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
  rememberMe: z.boolean().default(false),
});

export interface CustomerAuthResult {
  success: boolean;
  customer?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    displayName: string;
    image?: string | null;
    tenantId: number;
    isEmailVerified: boolean;
    membershipType: string;
    preferences: any;
  };
  error?: string;
  requiresEmailVerification?: boolean;
}

export interface GoogleOAuthProfile {
  id: string;
  email: string;
  name: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  email_verified: boolean;
  locale?: string;
}

class CustomerAuthService {
  /**
   * Authenticate customer with email/password
   */
  async authenticateWithCredentials(
    email: string, 
    password: string, 
    tenantId: number,
    ipAddress?: string
  ): Promise<CustomerAuthResult> {
    try {
      console.log(`🔐 Customer auth attempt: ${email} for tenant ${tenantId}`);

      // Validate tenant allows email/password auth
      const tenantConfig = await tenantContextService.getTenantOAuthConfig(tenantId);
      
      // Find customer in tenant scope
      const [customer] = await db
        .select()
        .from(customers)
        .where(and(
          eq(customers.email, email),
          eq(customers.tenantId, tenantId),
          eq(customers.isActive, true)
        ))
        .limit(1);

      if (!customer) {
        console.log(`❌ Customer not found: ${email} in tenant ${tenantId}`);
        return { success: false, error: "Invalid credentials" };
      }

      // Check if account is locked
      if (customer.lockedUntil && customer.lockedUntil > new Date()) {
        const lockExpiry = customer.lockedUntil.toISOString();
        console.log(`🔒 Customer account locked until: ${lockExpiry}`);
        return { 
          success: false, 
          error: `Account is locked. Try again after ${lockExpiry}` 
        };
      }

      // Verify password (only if customer has password set)
      if (!customer.password) {
        console.log(`❌ Customer ${email} has no password (OAuth-only account)`);
        return { 
          success: false, 
          error: "Please sign in with Google" 
        };
      }

      const isPasswordValid = await compare(password, customer.password);
      
      if (!isPasswordValid) {
        // Increment failed attempts
        const newFailedAttempts = (customer.failedLoginAttempts || 0) + 1;
        const shouldLock = newFailedAttempts >= 5;
        
        await db
          .update(customers)
          .set({
            failedLoginAttempts: newFailedAttempts,
            lockedUntil: shouldLock ? new Date(Date.now() + 30 * 60 * 1000) : null, // 30 min lock
            updatedAt: new Date(),
          })
          .where(eq(customers.id, customer.id));

        console.log(`❌ Invalid password for ${email}, attempts: ${newFailedAttempts}`);
        return { 
          success: false, 
          error: shouldLock 
            ? "Account locked due to multiple failed attempts" 
            : "Invalid credentials" 
        };
      }

      // Check email verification requirement
      if (tenantConfig.requireEmailVerification && !customer.emailVerified) {
        console.log(`⚠️ Customer ${email} email not verified`);
        return {
          success: false,
          error: "Please verify your email address",
          requiresEmailVerification: true,
        };
      }

      // Success - reset failed attempts and update login info
      await db
        .update(customers)
        .set({
          failedLoginAttempts: 0,
          lockedUntil: null,
          lastLoginAt: new Date(),
          lastLoginIP: ipAddress || null,
          updatedAt: new Date(),
        })
        .where(eq(customers.id, customer.id));

      console.log(`✅ Customer authentication successful: ${email}`);

      return {
        success: true,
        customer: {
          id: customer.id,
          email: customer.email,
          firstName: customer.firstName,
          lastName: customer.lastName || '',
          displayName: customer.displayName || `${customer.firstName} ${customer.lastName || ''}`.trim(),
          image: customer.image,
          tenantId: customer.tenantId,
          isEmailVerified: !!customer.emailVerified,
          membershipType: customer.membershipType || 'basic',
          preferences: customer.preferences || {},
        },
      };

    } catch (error) {
      console.error('🚨 Customer authentication error:', error);
      return { success: false, error: "Authentication failed" };
    }
  }

  /**
   * Authenticate or create customer with Google OAuth
   */
  async authenticateWithGoogle(
    profile: GoogleOAuthProfile,
    account: any,
    tenantId: number,
    ipAddress?: string
  ): Promise<CustomerAuthResult> {
    try {
      console.log(`🔐 Google OAuth for customer: ${profile.email} in tenant ${tenantId}`);

      // Validate tenant allows Google OAuth
      const tenantConfig = await tenantContextService.getTenantOAuthConfig(tenantId);
      
      if (!tenantConfig.allowGoogleOAuth) {
        console.log(`❌ Google OAuth disabled for tenant ${tenantId}`);
        return { success: false, error: "Google sign-in is not enabled" };
      }

      // Check allowed domains if configured
      if (tenantConfig.allowedDomains.length > 0) {
        const emailDomain = profile.email.split('@')[1];
        if (!tenantConfig.allowedDomains.includes(emailDomain)) {
          console.log(`❌ Domain ${emailDomain} not allowed for tenant ${tenantId}`);
          return { success: false, error: "Your email domain is not authorized" };
        }
      }

      // Verify Google email is verified
      if (!profile.email_verified) {
        console.log(`❌ Google email not verified: ${profile.email}`);
        return { success: false, error: "Google email is not verified" };
      }

      // Check if customer already exists
      const [existingCustomer] = await db
        .select()
        .from(customers)
        .where(and(
          eq(customers.email, profile.email),
          eq(customers.tenantId, tenantId)
        ))
        .limit(1);

      let customer;

      if (existingCustomer) {
        // Update existing customer
        const [updatedCustomer] = await db
          .update(customers)
          .set({
            googleId: profile.id,
            image: profile.picture || existingCustomer.image,
            emailVerified: new Date(),
            isEmailVerified: true,
            lastLoginAt: new Date(),
            lastLoginIP: ipAddress || null,
            failedLoginAttempts: 0,
            lockedUntil: null,
            updatedAt: new Date(),
          })
          .where(eq(customers.id, existingCustomer.id))
          .returning();

        customer = updatedCustomer;
        console.log(`✅ Updated existing customer: ${profile.email}`);
      } else {
        // Create new customer
        const customerId = createId();
        const displayName = profile.name || `${profile.given_name || ''} ${profile.family_name || ''}`.trim();
        
        const [newCustomer] = await db
          .insert(customers)
          .values({
            id: customerId,
            tenantId: tenantId,
            email: profile.email,
            firstName: profile.given_name || profile.name?.split(' ')[0] || 'User',
            lastName: profile.family_name || profile.name?.split(' ').slice(1).join(' ') || '',
            displayName: displayName || profile.email.split('@')[0],
            image: profile.picture,
            googleId: profile.id,
            emailVerified: new Date(),
            isEmailVerified: true,
            membershipType: 'basic',
            isActive: true,
            lastLoginAt: new Date(),
            lastLoginIP: ipAddress || null,
            preferences: {
              language: profile.locale || 'en',
            },
            termsAcceptedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          })
          .returning();

        customer = newCustomer;
        console.log(`✅ Created new customer: ${profile.email}`);
      }

      // Create or update OAuth account record
      await db
        .insert(customerOAuthAccounts)
        .values({
          id: createId(),
          customerId: customer.id,
          tenantId: tenantId,
          provider: 'google',
          providerAccountId: profile.id,
          type: account.type || 'oauth',
          accessToken: account.access_token,
          refreshToken: account.refresh_token,
          idToken: account.id_token,
          expiresAt: account.expires_at,
          tokenType: account.token_type,
          scope: account.scope,
          providerProfile: {
            name: profile.name,
            email: profile.email,
            picture: profile.picture,
            locale: profile.locale,
            emailVerified: profile.email_verified,
          },
          lastUsed: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .onConflictDoUpdate({
          target: [customerOAuthAccounts.customerId, customerOAuthAccounts.provider],
          set: {
            accessToken: account.access_token,
            refreshToken: account.refresh_token,
            idToken: account.id_token,
            expiresAt: account.expires_at,
            providerProfile: {
              name: profile.name,
              email: profile.email,
              picture: profile.picture,
              locale: profile.locale,
              emailVerified: profile.email_verified,
            },
            lastUsed: new Date(),
            updatedAt: new Date(),
          },
        });

      return {
        success: true,
        customer: {
          id: customer.id,
          email: customer.email,
          firstName: customer.firstName,
          lastName: customer.lastName || '',
          displayName: customer.displayName || `${customer.firstName} ${customer.lastName || ''}`.trim(),
          image: customer.image,
          tenantId: customer.tenantId,
          isEmailVerified: true,
          membershipType: customer.membershipType || 'basic',
          preferences: customer.preferences || {},
        },
      };

    } catch (error) {
      console.error('🚨 Google OAuth authentication error:', error);
      return { success: false, error: "Google authentication failed" };
    }
  }

  /**
   * Create customer account with email/password
   */
  async createCustomer(
    data: z.infer<typeof customerSignupSchema>,
    tenantId: number,
    ipAddress?: string
  ): Promise<CustomerAuthResult> {
    try {
      console.log(`👤 Creating customer: ${data.email} in tenant ${tenantId}`);

      // Check if customer already exists in tenant
      const [existingCustomer] = await db
        .select()
        .from(customers)
        .where(and(
          eq(customers.email, data.email),
          eq(customers.tenantId, tenantId)
        ))
        .limit(1);

      if (existingCustomer) {
        console.log(`❌ Customer already exists: ${data.email}`);
        return { success: false, error: "Account already exists" };
      }

      // Hash password if provided
      let hashedPassword: string | null = null;
      if (data.password) {
        hashedPassword = await hash(data.password, 12);
      }

      // Create customer
      const customerId = createId();
      const displayName = `${data.firstName} ${data.lastName}`.trim();

      const [newCustomer] = await db
        .insert(customers)
        .values({
          id: customerId,
          tenantId: tenantId,
          email: data.email,
          firstName: data.firstName,
          lastName: data.lastName,
          displayName: displayName,
          password: hashedPassword,
          mobileCountryCode: data.phone?.countryCode,
          mobileNumber: data.phone?.number,
          dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : null,
          membershipType: 'basic',
          isActive: true,
          marketingEmails: data.marketingEmails,
          termsAcceptedAt: new Date(),
          preferences: data.preferences || { language: 'en' },
          lastLoginIP: ipAddress || null,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      console.log(`✅ Customer created successfully: ${data.email}`);

      return {
        success: true,
        customer: {
          id: newCustomer.id,
          email: newCustomer.email,
          firstName: newCustomer.firstName,
          lastName: newCustomer.lastName || '',
          displayName: newCustomer.displayName || displayName,
          image: newCustomer.image,
          tenantId: newCustomer.tenantId,
          isEmailVerified: false,
          membershipType: newCustomer.membershipType || 'basic',
          preferences: newCustomer.preferences || {},
        },
        requiresEmailVerification: !newCustomer.emailVerified,
      };

    } catch (error) {
      console.error('🚨 Customer creation error:', error);
      return { success: false, error: "Failed to create account" };
    }
  }

  /**
   * Get customer by ID with tenant validation
   */
  async getCustomerById(customerId: string, tenantId: number): Promise<CustomerAuthResult['customer'] | null> {
    try {
      const [customer] = await db
        .select()
        .from(customers)
        .where(and(
          eq(customers.id, customerId),
          eq(customers.tenantId, tenantId),
          eq(customers.isActive, true)
        ))
        .limit(1);

      if (!customer) return null;

      return {
        id: customer.id,
        email: customer.email,
        firstName: customer.firstName,
        lastName: customer.lastName || '',
        displayName: customer.displayName || `${customer.firstName} ${customer.lastName || ''}`.trim(),
        image: customer.image,
        tenantId: customer.tenantId,
        isEmailVerified: !!customer.emailVerified,
        membershipType: customer.membershipType || 'basic',
        preferences: customer.preferences || {},
      };
    } catch (error) {
      console.error('🚨 Error getting customer:', error);
      return null;
    }
  }

  /**
   * Verify customer email
   */
  async verifyCustomerEmail(customerId: string, tenantId: number): Promise<boolean> {
    try {
      const [result] = await db
        .update(customers)
        .set({
          emailVerified: new Date(),
          isEmailVerified: true,
          updatedAt: new Date(),
        })
        .where(and(
          eq(customers.id, customerId),
          eq(customers.tenantId, tenantId)
        ))
        .returning({ id: customers.id });

      return !!result;
    } catch (error) {
      console.error('🚨 Error verifying customer email:', error);
      return false;
    }
  }
}

// Export singleton instance
export const customerAuthService = new CustomerAuthService();