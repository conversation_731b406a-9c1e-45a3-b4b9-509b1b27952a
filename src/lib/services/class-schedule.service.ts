import { db } from "@/lib/db";
import {
  class_schedules,
  classes,
  locations,
  facilities,
  users,
  type ClassSchedule
} from "@/lib/db/schema";
import { eq, and, desc, sql, gte, lte, lt, gt, ne } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";
import {
  type ScheduleConflict,
  type ConflictValidationResult,
  type AlternativeTimeSlot,
  type ConflictCheckParams,
  type ConflictType,
  type ConflictSeverity
} from "@/lib/types/schedule-conflict.types";
import {
  withTimeout,
  PerformanceMonitor,
  conflictCache,
  conflictCircuitBreaker,
  QueryOptimizer,
  BatchProcessor
} from "@/lib/utils/performance";

/**
 * Class Schedule Service
 * 
 * Service untuk manage class schedules dengan pattern yang sama seperti ClassService.
 * Ini kayak "manager" yang handle semua operasi CRUD untuk class schedules.
 * 
 * Pattern ini udah terbukti berhasil tanpa bug di classes, categories, dan subcategories.
 * 
 * Kenapa pakai static methods?
 * - Simple dan straightforward
 * - Tidak perlu instantiate class
 * - Mudah di-test dan di-maintain
 * - Konsisten dengan service lain yang sudah ada
 */

// Interface untuk search results
export interface ClassScheduleSearchResult {
  schedules: ClassSchedule[];
  total: number;
  hasMore: boolean;
}

// Interface untuk schedule dengan relasi
export interface ClassScheduleWithRelations extends ClassSchedule {
  class_name?: string;
  location_name?: string;
  facility_name?: string;
  instructor_name?: string;
}

/**
 * ClassScheduleService - Service untuk manage class schedules
 * 
 * Menggunakan static methods pattern yang simple dan proven.
 * Tidak pakai BaseService yang kompleks, langsung ke database dengan Drizzle ORM.
 */
export class ClassScheduleService {
  /**
   * Search class schedules dengan filtering dan pagination
   * 
   * Ini method utama untuk ambil data class schedules dengan berbagai filter:
   * - tenantId: wajib untuk tenant isolation
   * - classId: optional, filter berdasarkan class
   * - locationId: optional, filter berdasarkan location
   * - facilityId: optional, filter berdasarkan facility
   * - staffId: optional, filter berdasarkan instructor
   * - startDate & endDate: optional, filter berdasarkan tanggal
   * - search: optional, search berdasarkan nama class atau instructor
   * - limit & offset: untuk pagination
   */
  static async searchSchedules(
    tenantId: number,
    classId?: string,
    locationId?: string,
    facilityId?: string,
    staffId?: string,
    startDate?: string,
    endDate?: string,
    search?: string,
    limit = 20,
    offset = 0
  ): Promise<ClassScheduleSearchResult> {
    try {
      // Build where conditions - mulai dari tenant isolation
      const conditions = [eq(class_schedules.tenant_id, tenantId)];

      // Add optional filters
      if (classId) {
        conditions.push(eq(class_schedules.class_id, classId));
      }

      if (locationId) {
        conditions.push(eq(class_schedules.location_id, locationId));
      }

      if (facilityId) {
        conditions.push(eq(class_schedules.facility_id, facilityId));
      }

      if (staffId) {
        conditions.push(eq(class_schedules.staff_id, staffId));
      }

      // Date range filter - prioritize start_time over start_date for better coverage
      if (startDate) {
        conditions.push(
          sql`(
            COALESCE(${class_schedules.start_date}, DATE(${class_schedules.start_time})) >= ${startDate}
          )`
        );
      }

      if (endDate) {
        conditions.push(
          sql`(
            COALESCE(${class_schedules.end_date}, DATE(${class_schedules.end_time})) <= ${endDate}
          )`
        );
      }

      const whereConditions = and(...conditions);

      // Search functionality - search di nama class atau instructor
      if (search && search.trim()) {
        // Untuk search, kita perlu join dengan tables lain
        const searchResults = await db
          .select({
            schedule: class_schedules,
            class_name: classes.name,
            instructor_name: users.name,
          })
          .from(class_schedules)
          .leftJoin(classes, eq(class_schedules.class_id, classes.id))
          .leftJoin(users, eq(class_schedules.staff_id, users.id))
          .where(
            and(
              whereConditions,
              sql`(${classes.name} ILIKE ${`%${search}%`} OR ${users.name} ILIKE ${`%${search}%`})`
            )
          )
          .orderBy(desc(sql`COALESCE(${class_schedules.start_date}, DATE(${class_schedules.start_time}))`), desc(class_schedules.start_time))
          .limit(limit)
          .offset(offset);

        // Get total count untuk pagination
        const [{ count }] = await db
          .select({ count: sql<number>`count(*)` })
          .from(class_schedules)
          .leftJoin(classes, eq(class_schedules.class_id, classes.id))
          .leftJoin(users, eq(class_schedules.staff_id, users.id))
          .where(
            and(
              whereConditions,
              sql`(${classes.name} ILIKE ${`%${search}%`} OR ${users.name} ILIKE ${`%${search}%`})`
            )
          );

        const total = Number(count);
        const hasMore = offset + limit < total;

        return {
          schedules: searchResults.map(result => result.schedule),
          total,
          hasMore,
        };
      }

      // Regular query tanpa search
      const scheduleResults = await db
        .select()
        .from(class_schedules)
        .where(whereConditions)
        .orderBy(desc(sql`COALESCE(${class_schedules.start_date}, DATE(${class_schedules.start_time}))`), desc(class_schedules.start_time))
        .limit(limit)
        .offset(offset);

      // Get total count untuk pagination
      const [{ count }] = await db
        .select({ count: sql<number>`count(*)` })
        .from(class_schedules)
        .where(whereConditions);

      const total = Number(count);
      const hasMore = offset + limit < total;

      return {
        schedules: scheduleResults,
        total,
        hasMore,
      };
    } catch (error) {
      console.error("Error searching class schedules:", error);
      throw error;
    }
  }

  /**
   * Search public class schedules (non-private) dengan filtering dan pagination
   *
   * Method khusus untuk public API yang hanya menampilkan schedules yang tidak private.
   * Sama seperti searchSchedules tapi dengan filter tambahan is_private = false atau null.
   *
   * Parameters sama seperti searchSchedules:
   * - tenantId: wajib untuk tenant isolation
   * - classId: optional, filter berdasarkan class
   * - locationId: optional, filter berdasarkan location
   * - facilityId: optional, filter berdasarkan facility
   * - staffId: optional, filter berdasarkan instructor
   * - startDate & endDate: optional, filter berdasarkan tanggal
   * - search: optional, search berdasarkan nama class atau instructor
   * - limit & offset: untuk pagination
   */
  static async searchPublicSchedules(
    tenantId: number,
    classId?: string,
    locationId?: string,
    facilityId?: string,
    staffId?: string,
    startDate?: string,
    endDate?: string,
    search?: string,
    limit = 20,
    offset = 0
  ): Promise<ClassScheduleSearchResult> {
    try {
      // Build where conditions - mulai dari tenant isolation
      const conditions = [eq(class_schedules.tenant_id, tenantId)];

      // Filter untuk hanya schedules yang tidak private
      // is_private = false OR is_private IS NULL
      conditions.push(
        sql`(${class_schedules.is_private} = false OR ${class_schedules.is_private} IS NULL)`
      );

      // Add optional filters
      if (classId) {
        conditions.push(eq(class_schedules.class_id, classId));
      }

      if (locationId) {
        conditions.push(eq(class_schedules.location_id, locationId));
      }

      if (facilityId) {
        conditions.push(eq(class_schedules.facility_id, facilityId));
      }

      if (staffId) {
        conditions.push(eq(class_schedules.staff_id, staffId));
      }

      // Date range filter - prioritize start_time over start_date for better coverage
      if (startDate) {
        conditions.push(
          sql`(
            COALESCE(${class_schedules.start_date}, DATE(${class_schedules.start_time})) >= ${startDate}
          )`
        );
      }

      if (endDate) {
        conditions.push(
          sql`(
            COALESCE(${class_schedules.end_date}, DATE(${class_schedules.end_time})) <= ${endDate}
          )`
        );
      }

      const whereConditions = and(...conditions);

      // Search functionality - search di nama class atau instructor
      if (search && search.trim()) {
        // Untuk search, kita perlu join dengan tables lain
        const searchResults = await db
          .select({
            schedule: class_schedules,
            class_name: classes.name,
            instructor_name: users.name,
          })
          .from(class_schedules)
          .leftJoin(classes, eq(class_schedules.class_id, classes.id))
          .leftJoin(users, eq(class_schedules.staff_id, users.id))
          .where(
            and(
              whereConditions,
              sql`(${classes.name} ILIKE ${`%${search}%`} OR ${users.name} ILIKE ${`%${search}%`})`
            )
          )
          .orderBy(desc(sql`COALESCE(${class_schedules.start_date}, DATE(${class_schedules.start_time}))`), desc(class_schedules.start_time))
          .limit(limit)
          .offset(offset);

        // Get total count untuk pagination
        const [{ count }] = await db
          .select({ count: sql<number>`count(*)` })
          .from(class_schedules)
          .leftJoin(classes, eq(class_schedules.class_id, classes.id))
          .leftJoin(users, eq(class_schedules.staff_id, users.id))
          .where(
            and(
              whereConditions,
              sql`(${classes.name} ILIKE ${`%${search}%`} OR ${users.name} ILIKE ${`%${search}%`})`
            )
          );

        const total = Number(count);
        const hasMore = offset + limit < total;

        return {
          schedules: searchResults.map(result => result.schedule),
          total,
          hasMore,
        };
      }

      // Regular query tanpa search
      const scheduleResults = await db
        .select()
        .from(class_schedules)
        .where(whereConditions)
        .orderBy(desc(sql`COALESCE(${class_schedules.start_date}, DATE(${class_schedules.start_time}))`), desc(class_schedules.start_time))
        .limit(limit)
        .offset(offset);

      // Get total count untuk pagination
      const [{ count }] = await db
        .select({ count: sql<number>`count(*)` })
        .from(class_schedules)
        .where(whereConditions);

      const total = Number(count);
      const hasMore = offset + limit < total;

      return {
        schedules: scheduleResults,
        total,
        hasMore,
      };
    } catch (error) {
      console.error("Error searching public class schedules:", error);
      throw error;
    }
  }

  /**
   * Get all class schedules by tenant ID
   *
   * Method simple untuk ambil semua class schedules dalam satu tenant.
   * Biasanya dipake untuk dropdown atau list sederhana.
   */
  static async getByTenantId(tenantId: number): Promise<ClassSchedule[]> {
    return await db
      .select()
      .from(class_schedules)
      .where(eq(class_schedules.tenant_id, tenantId))
      .orderBy(desc(class_schedules.start_date));
  }

  /**
   * Get class schedules by class ID
   * 
   * Ambil semua schedules untuk class tertentu.
   * Berguna untuk melihat jadwal class tertentu.
   */
  static async getByClassId(classId: string): Promise<ClassSchedule[]> {
    return await db
      .select()
      .from(class_schedules)
      .where(eq(class_schedules.class_id, classId))
      .orderBy(desc(class_schedules.start_date));
  }

  /**
   * Get class schedules by location ID
   * 
   * Ambil semua schedules di location tertentu.
   * Berguna untuk melihat jadwal per location.
   */
  static async getByLocationId(locationId: string): Promise<ClassSchedule[]> {
    return await db
      .select()
      .from(class_schedules)
      .where(eq(class_schedules.location_id, locationId))
      .orderBy(desc(class_schedules.start_date));
  }

  /**
   * Get class schedules by instructor/staff ID
   * 
   * Ambil semua schedules untuk instructor tertentu.
   * Berguna untuk melihat jadwal mengajar instructor.
   */
  static async getByInstructorId(staffId: string): Promise<ClassSchedule[]> {
    return await db
      .select()
      .from(class_schedules)
      .where(eq(class_schedules.staff_id, staffId))
      .orderBy(desc(class_schedules.start_date));
  }

  /**
   * Get class schedule by ID dengan relasi
   *
   * Ambil single class schedule berdasarkan ID dengan informasi relasi.
   * Return null kalau tidak ditemukan.
   */
  static async getById(id: string): Promise<ClassScheduleWithRelations | null> {
    const [scheduleResult] = await db
      .select({
        schedule: class_schedules,
        class_name: classes.name,
        location_name: locations.name,
        facility_name: facilities.name,
        instructor_name: users.name,
      })
      .from(class_schedules)
      .leftJoin(classes, eq(class_schedules.class_id, classes.id))
      .leftJoin(locations, eq(class_schedules.location_id, locations.id))
      .leftJoin(facilities, eq(class_schedules.facility_id, facilities.id))
      .leftJoin(users, eq(class_schedules.staff_id, users.id))
      .where(eq(class_schedules.id, id))
      .limit(1);

    if (!scheduleResult) return null;

    return {
      ...scheduleResult.schedule,
      class_name: scheduleResult.class_name || undefined,
      location_name: scheduleResult.location_name || undefined,
      facility_name: scheduleResult.facility_name || undefined,
      instructor_name: scheduleResult.instructor_name || undefined,
    };
  }

  /**
   * Get public class schedule by ID dengan relasi
   *
   * Ambil single class schedule berdasarkan ID dengan informasi relasi,
   * tapi hanya untuk schedules yang tidak private (is_private = false atau null).
   * Includes tenant isolation untuk security.
   * Return null kalau tidak ditemukan atau private.
   */
  static async getPublicById(id: string, tenantId: number): Promise<ClassScheduleWithRelations | null> {
    const [scheduleResult] = await db
      .select({
        schedule: class_schedules,
        class_name: classes.name,
        location_name: locations.name,
        facility_name: facilities.name,
        instructor_name: users.name,
      })
      .from(class_schedules)
      .leftJoin(classes, eq(class_schedules.class_id, classes.id))
      .leftJoin(locations, eq(class_schedules.location_id, locations.id))
      .leftJoin(facilities, eq(class_schedules.facility_id, facilities.id))
      .leftJoin(users, eq(class_schedules.staff_id, users.id))
      .where(
        and(
          eq(class_schedules.id, id),
          eq(class_schedules.tenant_id, tenantId),
          // Only public schedules: is_private = false OR is_private IS NULL
          sql`(${class_schedules.is_private} = false OR ${class_schedules.is_private} IS NULL)`
        )
      )
      .limit(1);

    if (!scheduleResult) return null;

    return {
      ...scheduleResult.schedule,
      class_name: scheduleResult.class_name || undefined,
      location_name: scheduleResult.location_name || undefined,
      facility_name: scheduleResult.facility_name || undefined,
      instructor_name: scheduleResult.instructor_name || undefined,
    };
  }

  /**
   * FAANG-Level Schedule Conflict Detection
   *
   * Comprehensive conflict detection dengan atomic transactions,
   * efficient queries, dan detailed conflict analysis.
   */

  /**
   * Check for schedule conflicts - OPTIMIZED VERSION
   *
   * High-performance conflict detection dengan timeout handling,
   * caching, circuit breaker, dan progressive loading.
   */
  static async checkScheduleConflicts(params: ConflictCheckParams): Promise<ConflictValidationResult> {
    return await PerformanceMonitor.measure(
      'checkScheduleConflicts',
      () => this.checkScheduleConflictsInternal(params),
      2000 // 2 second threshold
    );
  }

  /**
   * Internal optimized conflict detection method
   */
  private static async checkScheduleConflictsInternal(params: ConflictCheckParams): Promise<ConflictValidationResult> {
    // Generate cache key
    const cacheKey = QueryOptimizer.generateCacheKey(params);

    // Check cache first
    const cachedResult = conflictCache.get<ConflictValidationResult>(cacheKey);
    if (cachedResult) {
      console.log(`⚡ Cache hit for conflict check: ${cacheKey}`);
      return cachedResult;
    }

    // Use circuit breaker untuk resilience
    return await conflictCircuitBreaker.execute(async () => {
      return await withTimeout(
        this.performConflictDetection(params, cacheKey),
        5000, // 5 second timeout
        'Conflict detection timed out'
      );
    });
  }

  /**
   * Core conflict detection logic dengan optimizations
   */
  private static async performConflictDetection(
    params: ConflictCheckParams,
    cacheKey: string
  ): Promise<ConflictValidationResult> {
    const startTime = Date.now();
    let queryTime = 0;
    let analysisTime = 0;

    try {
      const conflicts: ScheduleConflict[] = [];
      const warnings: string[] = [];

      // Optimize query based on parameters
      const queryLimits = QueryOptimizer.calculateQueryLimits(params.startTime, params.endTime);
      const useFastPath = QueryOptimizer.shouldUseFastPath(params);

      // Build optimized query conditions
      const conditions = [eq(class_schedules.tenant_id, params.tenantId)];

      // Exclude current schedule if updating
      if (params.excludeScheduleId) {
        conditions.push(ne(class_schedules.id, params.excludeScheduleId));
      }

      // Time overlap detection with optional buffer time
      const bufferMinutes = params.bufferMinutes || 0;
      const bufferMs = bufferMinutes * 60 * 1000;

      // Adjust time range untuk buffer
      const adjustedStartTime = new Date(params.startTime.getTime() - bufferMs);
      const adjustedEndTime = new Date(params.endTime.getTime() + bufferMs);

      // Add time window optimization untuk large date ranges
      const timeWindowStart = new Date(adjustedStartTime.getTime() - (queryLimits.timeWindowHours * 60 * 60 * 1000));
      const timeWindowEnd = new Date(adjustedEndTime.getTime() + (queryLimits.timeWindowHours * 60 * 60 * 1000));

      conditions.push(
        // Schedule starts before our adjusted end time
        lt(class_schedules.start_time, adjustedEndTime),
        // Schedule ends after our adjusted start time
        gt(class_schedules.end_time, adjustedStartTime),
        // Limit time window untuk performance
        gte(class_schedules.start_time, timeWindowStart),
        lte(class_schedules.start_time, timeWindowEnd)
      );

      // Add resource-specific conditions untuk better index usage
      if (params.locationId) {
        conditions.push(eq(class_schedules.location_id, params.locationId));
      }
      if (params.facilityId) {
        conditions.push(eq(class_schedules.facility_id, params.facilityId));
      }
      if (params.staffId) {
        conditions.push(eq(class_schedules.staff_id, params.staffId));
      }

      const whereConditions = and(...conditions);

      // Execute optimized query dengan limits
      const queryStartTime = Date.now();

      let potentialConflicts;

      if (useFastPath) {
        // Fast path: limited query untuk simple cases
        potentialConflicts = await db
          .select({
            id: class_schedules.id,
            class_id: class_schedules.class_id,
            tenant_id: class_schedules.tenant_id,
            location_id: class_schedules.location_id,
            facility_id: class_schedules.facility_id,
            staff_id: class_schedules.staff_id,
            start_time: class_schedules.start_time,
            end_time: class_schedules.end_time,
            duration: class_schedules.duration,
            pax: class_schedules.pax,
            repeat_rule: class_schedules.repeat_rule,
            className: classes.name,
            locationName: locations.name,
            facilityName: facilities.name,
          })
          .from(class_schedules)
          .leftJoin(classes, eq(class_schedules.class_id, classes.id))
          .leftJoin(locations, eq(class_schedules.location_id, locations.id))
          .leftJoin(facilities, eq(class_schedules.facility_id, facilities.id))
          .where(whereConditions)
          .limit(queryLimits.maxRecords)
          .orderBy(class_schedules.start_time);
      } else {
        // Full query untuk complex cases
        potentialConflicts = await db
          .select({
            id: class_schedules.id,
            class_id: class_schedules.class_id,
            tenant_id: class_schedules.tenant_id,
            location_id: class_schedules.location_id,
            facility_id: class_schedules.facility_id,
            staff_id: class_schedules.staff_id,
            start_date: class_schedules.start_date,
            end_date: class_schedules.end_date,
            start_time: class_schedules.start_time,
            end_time: class_schedules.end_time,
            duration: class_schedules.duration,
            pax: class_schedules.pax,
            repeat_rule: class_schedules.repeat_rule,
            className: classes.name,
            locationName: locations.name,
            facilityName: facilities.name,
          })
          .from(class_schedules)
          .leftJoin(classes, eq(class_schedules.class_id, classes.id))
          .leftJoin(locations, eq(class_schedules.location_id, locations.id))
          .leftJoin(facilities, eq(class_schedules.facility_id, facilities.id))
          .where(whereConditions)
          .limit(queryLimits.maxRecords * 2) // Allow more records untuk complex cases
          .orderBy(class_schedules.start_time);
      }

      queryTime = Date.now() - queryStartTime;

      // Progressive conflict analysis dengan batching
      const analysisStartTime = Date.now();
      const batchProcessor = new BatchProcessor(10, 50); // 10 items per batch, 50ms delay

      // Separate direct conflicts dan recurring conflicts untuk optimization
      const directConflicts: ScheduleConflict[] = [];
      const recurringConflicts: ScheduleConflict[] = [];

      // Process direct conflicts first (faster)
      const directResults = await batchProcessor.processBatches(
        potentialConflicts,
        async (batch) => {
          const batchConflicts: ScheduleConflict[] = [];
          for (const schedule of batch) {
            const conflict = await this.analyzeScheduleConflict(schedule, params);
            if (conflict) {
              batchConflicts.push(conflict);
            }
          }
          return batchConflicts;
        }
      );

      directConflicts.push(...directResults.flat());

      // Process recurring conflicts only if needed dan not too many direct conflicts
      if (directConflicts.length < 10 && !useFastPath) {
        const recurringSchedules = potentialConflicts.filter(s =>
          s.repeat_rule && s.repeat_rule !== 'none'
        );

        if (recurringSchedules.length > 0) {
          const recurringResults = await batchProcessor.processBatches(
            recurringSchedules.slice(0, 20), // Limit recurring analysis
            async (batch) => {
              const batchConflicts: ScheduleConflict[] = [];
              for (const schedule of batch) {
                const conflicts = await this.analyzeRecurringScheduleConflictsOptimized(schedule, params);
                batchConflicts.push(...conflicts);
              }
              return batchConflicts;
            }
          );

          recurringConflicts.push(...recurringResults.flat());
        }
      }

      conflicts.push(...directConflicts, ...recurringConflicts);

      analysisTime = Date.now() - analysisStartTime;

      // Determine if we can proceed
      const blockingConflicts = conflicts.filter(c => c.severity === 'BLOCKING');
      const canProceed = blockingConflicts.length === 0;

      // Generate suggestions only if needed dan not too many conflicts
      let suggestions: AlternativeTimeSlot[] | undefined;
      if (conflicts.length > 0 && conflicts.length <= 5) {
        suggestions = await this.generateAlternativeTimeSlotsOptimized(params, conflicts);
      }

      const totalTime = Date.now() - startTime;

      // Create result
      const result: ConflictValidationResult = {
        hasConflicts: conflicts.length > 0,
        conflicts: conflicts.slice(0, 20), // Limit conflicts returned
        canProceed,
        suggestions,
        warnings: warnings.length > 0 ? warnings : undefined,
      };

      // Cache result untuk future requests (shorter TTL untuk high-conflict scenarios)
      const cacheTTL = conflicts.length > 0 ? 60000 : 300000; // 1 min vs 5 min
      conflictCache.set(cacheKey, result, cacheTTL);

      // Enhanced performance logging
      if (totalTime > 2000) { // Log slow queries (> 2 seconds)
        console.warn(`🐌 Slow conflict detection query:`, {
          totalTime: `${totalTime}ms`,
          queryTime: `${queryTime}ms`,
          analysisTime: `${analysisTime}ms`,
          schedulesChecked: potentialConflicts.length,
          conflictsFound: conflicts.length,
          usedFastPath: useFastPath,
          cacheKey,
          params: {
            tenantId: params.tenantId,
            timeRange: `${params.startTime.toISOString()} - ${params.endTime.toISOString()}`,
            hasLocation: !!params.locationId,
            hasFacility: !!params.facilityId,
            hasStaff: !!params.staffId,
          }
        });
      } else if (totalTime > 1000) {
        console.log(`⚠️ Moderate conflict detection time: ${totalTime}ms`);
      } else {
        console.log(`⚡ Fast conflict detection: ${totalTime}ms`);
      }

      return result;
    } catch (error) {
      console.error("Error checking schedule conflicts:", error);

      // Return graceful degradation result
      return {
        hasConflicts: false,
        conflicts: [],
        canProceed: true,
        warnings: ["Conflict detection temporarily unavailable - proceeding with caution"]
      };
    }
  }

  /**
   * Analyze individual schedule conflict
   *
   * Determines conflict type, severity, dan details untuk specific schedule.
   */
  private static async analyzeScheduleConflict(
    existingSchedule: any,
    newParams: ConflictCheckParams
  ): Promise<ScheduleConflict | null> {
    // Calculate time overlap
    const overlapStart = new Date(Math.max(
      existingSchedule.start_time?.getTime() || 0,
      newParams.startTime.getTime()
    ));
    const overlapEnd = new Date(Math.min(
      existingSchedule.end_time?.getTime() || 0,
      newParams.endTime.getTime()
    ));

    // No actual overlap (shouldn't happen due to query, but safety check)
    if (overlapStart >= overlapEnd) {
      return null;
    }

    const overlapDuration = (overlapEnd.getTime() - overlapStart.getTime()) / (1000 * 60);

    // Determine conflict type and severity
    let conflictType: ConflictType;
    let severity: ConflictSeverity = 'WARNING';

    // Check for location conflicts
    if (newParams.locationId && existingSchedule.location_id === newParams.locationId) {
      conflictType = this.isExactTimeMatch(existingSchedule, newParams)
        ? 'EXACT_TIME_LOCATION'
        : 'PARTIAL_TIME_LOCATION';
      severity = 'BLOCKING'; // Location conflicts are always blocking
    }
    // Check for facility conflicts
    else if (newParams.facilityId && existingSchedule.facility_id === newParams.facilityId) {
      conflictType = this.isExactTimeMatch(existingSchedule, newParams)
        ? 'EXACT_TIME_FACILITY'
        : 'PARTIAL_TIME_FACILITY';
      severity = 'BLOCKING'; // Facility conflicts are always blocking
    }
    // Check for staff conflicts
    else if (newParams.staffId && existingSchedule.staff_id === newParams.staffId) {
      conflictType = 'STAFF_DOUBLE_BOOKING';
      severity = 'BLOCKING'; // Staff can't be in two places at once
    }
    // No resource conflict, just time overlap
    else {
      return null; // No conflict if different resources
    }

    return {
      conflictingSchedule: existingSchedule,
      conflictType,
      severity,
      conflictDetails: {
        timeOverlap: {
          start: overlapStart,
          end: overlapEnd,
          durationMinutes: overlapDuration,
        },
        resourceConflict: {
          location: existingSchedule.locationName,
          facility: existingSchedule.facilityName,
          staff: existingSchedule.staff_id,
        },
        affectedCapacity: {
          existingPax: existingSchedule.pax,
          newPax: 0, // Will be filled by caller
        },
      },
    };
  }

  /**
   * Check if two schedules have exact time match
   */
  private static isExactTimeMatch(existingSchedule: any, newParams: ConflictCheckParams): boolean {
    const existingStart = existingSchedule.start_time?.getTime();
    const existingEnd = existingSchedule.end_time?.getTime();
    const newStart = newParams.startTime.getTime();
    const newEnd = newParams.endTime.getTime();

    return existingStart === newStart && existingEnd === newEnd;
  }

  /**
   * Optimized recurring schedule conflict analysis
   *
   * Faster version dengan limited scope untuk prevent performance issues.
   */
  private static async analyzeRecurringScheduleConflictsOptimized(
    existingSchedule: any,
    newParams: ConflictCheckParams
  ): Promise<ScheduleConflict[]> {
    const conflicts: ScheduleConflict[] = [];

    if (!existingSchedule.repeat_rule || existingSchedule.repeat_rule === 'none') {
      return conflicts;
    }

    // Limit recurring analysis scope untuk performance
    const maxOccurrences = 12; // Max 12 occurrences
    const maxDaysAhead = 90; // Max 3 months ahead

    // Generate limited recurring dates
    const recurringDates = this.generateRecurringDatesOptimized(
      existingSchedule.start_time,
      existingSchedule.end_time,
      existingSchedule.repeat_rule,
      maxOccurrences,
      maxDaysAhead
    );

    // Quick conflict check untuk each recurring date
    for (const recurringDate of recurringDates) {
      const conflict = this.checkSingleRecurringConflict(
        existingSchedule,
        recurringDate,
        newParams
      );

      if (conflict) {
        conflicts.push(conflict);

        // Stop early if we found enough conflicts
        if (conflicts.length >= 5) {
          break;
        }
      }
    }

    return conflicts;
  }

  /**
   * Legacy method - kept untuk backward compatibility
   */
  private static async analyzeRecurringScheduleConflicts(
    existingSchedule: any,
    newParams: ConflictCheckParams
  ): Promise<ScheduleConflict[]> {
    // Use optimized version
    return this.analyzeRecurringScheduleConflictsOptimized(existingSchedule, newParams);
  }

  /**
   * Fast single recurring conflict check
   */
  private static checkSingleRecurringConflict(
    existingSchedule: any,
    recurringDate: Date,
    newParams: ConflictCheckParams
  ): ScheduleConflict | null {
    const conflicts: ScheduleConflict[] = [];

    if (!existingSchedule.repeat_rule || existingSchedule.repeat_rule === 'none') {
      return conflicts;
    }

    // Generate recurring dates untuk the existing schedule
    const recurringDates = this.generateRecurringDates(
      existingSchedule.start_time,
      existingSchedule.end_time,
      existingSchedule.repeat_rule,
      existingSchedule.end_date ? new Date(existingSchedule.end_date) : undefined
    );

    // Check each recurring date for conflicts
    for (const recurringDate of recurringDates) {
      // Skip if this recurring instance is too far in the past or future
      const daysDiff = Math.abs((recurringDate.getTime() - newParams.startTime.getTime()) / (1000 * 60 * 60 * 24));
      if (daysDiff > 365) continue; // Only check within 1 year

      // Check if the new schedule conflicts with this recurring instance
      const recurringStart = new Date(recurringDate);
      const recurringEnd = new Date(recurringDate);

      // Set the time from the original schedule
      if (existingSchedule.start_time) {
        const originalStart = new Date(existingSchedule.start_time);
        recurringStart.setHours(originalStart.getHours(), originalStart.getMinutes(), 0, 0);
      }

      if (existingSchedule.end_time) {
        const originalEnd = new Date(existingSchedule.end_time);
        recurringEnd.setHours(originalEnd.getHours(), originalEnd.getMinutes(), 0, 0);
      }

      // Check for time overlap
      const overlapStart = new Date(Math.max(recurringStart.getTime(), newParams.startTime.getTime()));
      const overlapEnd = new Date(Math.min(recurringEnd.getTime(), newParams.endTime.getTime()));

      if (overlapStart < overlapEnd) {
        // There's an overlap with this recurring instance
        const overlapDuration = (overlapEnd.getTime() - overlapStart.getTime()) / (1000 * 60);

        // Determine conflict type
        let conflictType: ConflictType;
        let severity: ConflictSeverity = 'WARNING';

        // Check resource conflicts
        if (newParams.locationId && existingSchedule.location_id === newParams.locationId) {
          conflictType = 'PARTIAL_TIME_LOCATION';
          severity = 'BLOCKING';
        } else if (newParams.facilityId && existingSchedule.facility_id === newParams.facilityId) {
          conflictType = 'PARTIAL_TIME_FACILITY';
          severity = 'BLOCKING';
        } else if (newParams.staffId && existingSchedule.staff_id === newParams.staffId) {
          conflictType = 'STAFF_DOUBLE_BOOKING';
          severity = 'BLOCKING';
        } else {
          continue; // No resource conflict
        }

        conflicts.push({
          conflictingSchedule: {
            ...existingSchedule,
            start_time: recurringStart,
            end_time: recurringEnd,
          },
          conflictType,
          severity,
          conflictDetails: {
            timeOverlap: {
              start: overlapStart,
              end: overlapEnd,
              durationMinutes: overlapDuration,
            },
            resourceConflict: {
              location: existingSchedule.locationName,
              facility: existingSchedule.facilityName,
              staff: existingSchedule.staff_id,
            },
            affectedCapacity: {
              existingPax: existingSchedule.pax,
              newPax: 0,
            },
          },
        });
      }
    }

    return conflicts;
  }

  /**
   * Generate recurring dates based on repeat rule
   */
  private static generateRecurringDates(
    startTime: Date,
    _endTime: Date,
    repeatRule: string,
    endDate?: Date
  ): Date[] {
    const dates: Date[] = [];
    const maxDates = 52; // Limit to 52 occurrences (1 year for weekly)
    const maxEndDate = endDate || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year from now

    let currentDate = new Date(startTime);
    let count = 0;

    while (currentDate <= maxEndDate && count < maxDates) {
      dates.push(new Date(currentDate));

      switch (repeatRule) {
        case 'daily':
          currentDate.setDate(currentDate.getDate() + 1);
          break;
        case 'weekly':
          currentDate.setDate(currentDate.getDate() + 7);
          break;
        case 'monthly':
          currentDate.setMonth(currentDate.getMonth() + 1);
          break;
        default:
          return dates; // Unknown repeat rule
      }

      count++;
    }

    return dates;
  }

  /**
   * Generate alternative time slot suggestions
   *
   * AI-powered suggestions untuk resolve conflicts dengan smart recommendations.
   */
  private static async generateAlternativeTimeSlots(
    params: ConflictCheckParams,
    _conflicts: ScheduleConflict[]
  ): Promise<AlternativeTimeSlot[]> {
    const suggestions: AlternativeTimeSlot[] = [];
    const requestedDuration = (params.endTime.getTime() - params.startTime.getTime()) / (1000 * 60);

    // Strategy 1: Same day, different time slots
    const sameDay = new Date(params.startTime);
    sameDay.setHours(0, 0, 0, 0);

    // Check for available slots throughout the day
    const timeSlots = this.generateTimeSlots(sameDay, requestedDuration);

    for (const slot of timeSlots) {
      const slotConflicts = await this.checkScheduleConflicts({
        ...params,
        startTime: slot.start,
        endTime: slot.end,
      });

      if (slotConflicts.canProceed) {
        suggestions.push({
          suggestedStartTime: slot.start,
          suggestedEndTime: slot.end,
          availableLocation: params.locationId,
          availableFacility: params.facilityId,
          reason: `Available time slot on same day`,
          confidence: 90,
        });

        if (suggestions.length >= 3) break; // Limit suggestions
      }
    }

    // Strategy 2: Same time, different location/facility
    if (params.locationId) {
      // Find alternative locations
      const alternativeLocations = await this.findAlternativeLocations(params);
      for (const location of alternativeLocations) {
        suggestions.push({
          suggestedStartTime: params.startTime,
          suggestedEndTime: params.endTime,
          availableLocation: location.id,
          reason: `Alternative location: ${location.name}`,
          confidence: 80,
        });
      }
    }

    return suggestions.slice(0, 5); // Return top 5 suggestions
  }

  /**
   * Generate time slots for a given day
   */
  private static generateTimeSlots(date: Date, durationMinutes: number) {
    const slots = [];
    const startHour = 6; // 6 AM
    const endHour = 22; // 10 PM

    for (let hour = startHour; hour <= endHour - Math.ceil(durationMinutes / 60); hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const start = new Date(date);
        start.setHours(hour, minute, 0, 0);

        const end = new Date(start);
        end.setMinutes(end.getMinutes() + durationMinutes);

        slots.push({ start, end });
      }
    }

    return slots;
  }

  /**
   * Find alternative locations for the same time slot
   */
  private static async findAlternativeLocations(params: ConflictCheckParams): Promise<Array<{id: string; name: string}>> {
    try {
      // Query available locations that don't have conflicts at the same time
      const availableLocations = await db
        .select({
          id: locations.id,
          name: locations.name,
        })
        .from(locations)
        .where(
          and(
            eq(locations.tenantId, params.tenantId),
            ne(locations.id, params.locationId || '') // Exclude current location
          )
        )
        .limit(5);

      // Filter out locations that have conflicts
      const conflictFreeLocations = [];
      for (const location of availableLocations) {
        const locationConflictCheck = await this.checkScheduleConflicts({
          ...params,
          locationId: location.id,
        });

        if (locationConflictCheck.canProceed) {
          conflictFreeLocations.push({
            id: location.id,
            name: location.name || 'Unknown Location'
          });
        }
      }

      return conflictFreeLocations;
    } catch (error) {
      console.error("Error finding alternative locations:", error);
      return [];
    }
  }

  /**
   * Create class schedule
   *
   * Enhanced dengan comprehensive conflict detection dan atomic transactions.
   * Includes validation untuk foreign key references dan schedule conflicts.
   */
  static async create(data: {
    tenant_id: number;
    class_id: string;
    location_id?: string;
    facility_id?: string;
    staff_id?: string;
    start_date?: string;
    end_date?: string;
    start_time?: Date;
    end_time?: Date;
    duration: number;
    calender_color?: string;
    repeat_rule?: string;
    pax?: number;
    waitlist?: number;
    allow_classpass?: boolean;
    is_private?: boolean;
    publish_now?: boolean;
    publish_at?: Date;
    auto_cancel_if_minimum_not_met?: boolean;
    booking_window_start?: Date;
    booking_window_end?: Date;
    check_in_window_start?: Date;
    check_in_window_end?: Date;
    late_cancellation_rule?: string;
  }): Promise<ClassSchedule> {
    // FAANG-Level Atomic Transaction dengan Conflict Detection
    return await db.transaction(async (tx) => {
      // Step 1: Validate foreign key references
      await this.validateForeignKeyReferences(data, tx);

      // Step 2: Comprehensive Conflict Detection
      if (data.start_time && data.end_time) {
        const conflictParams: ConflictCheckParams = {
          tenantId: data.tenant_id,
          startTime: data.start_time,
          endTime: data.end_time,
          locationId: data.location_id,
          facilityId: data.facility_id,
          staffId: data.staff_id,
        };

        const conflictResult = await this.checkScheduleConflicts(conflictParams);

        if (!conflictResult.canProceed) {
          const blockingConflicts = conflictResult.conflicts.filter(c => c.severity === 'BLOCKING');
          const conflictMessages = blockingConflicts.map(conflict => {
            const details = conflict.conflictDetails;
            const resource = details.resourceConflict.location || details.resourceConflict.facility || 'staff';
            const timeStr = `${details.timeOverlap.start.toLocaleTimeString()} - ${details.timeOverlap.end.toLocaleTimeString()}`;
            return `${conflict.conflictType.replace('_', ' ').toLowerCase()} conflict with existing schedule at ${resource} during ${timeStr}`;
          });

          const errorMessage = `Schedule conflict detected: ${conflictMessages.join('; ')}`;

          // Add suggestions if available
          if (conflictResult.suggestions && conflictResult.suggestions.length > 0) {
            const suggestionTexts = conflictResult.suggestions.slice(0, 3).map(s =>
              `${s.suggestedStartTime.toLocaleTimeString()} - ${s.suggestedEndTime.toLocaleTimeString()}`
            );
            throw new Error(`${errorMessage}. Suggested alternatives: ${suggestionTexts.join(', ')}`);
          }

          throw new Error(errorMessage);
        }

        // Log warnings if any
        if (conflictResult.warnings && conflictResult.warnings.length > 0) {
          console.warn("Schedule warnings:", conflictResult.warnings);
        }
      }

      // Step 3: Create the schedule
      const [scheduleResult] = await tx
        .insert(class_schedules)
        .values({
          id: createId(),
          tenant_id: data.tenant_id,
          class_id: data.class_id,
          location_id: data.location_id || null,
          facility_id: data.facility_id || null,
          staff_id: data.staff_id || null,
          start_date: data.start_date || null,
          end_date: data.end_date || null,
          start_time: data.start_time || null,
          end_time: data.end_time || null,
          duration: data.duration,
          calender_color: data.calender_color || null,
          repeat_rule: data.repeat_rule || "none",
          pax: data.pax || 1,
          waitlist: data.waitlist || 1,
          allow_classpass: data.allow_classpass || false,
          is_private: data.is_private || false,
          publish_now: data.publish_now || false,
          publish_at: data.publish_at || null,
          auto_cancel_if_minimum_not_met: data.auto_cancel_if_minimum_not_met || false,
          booking_window_start: data.booking_window_start || null,
          booking_window_end: data.booking_window_end || null,
          check_in_window_start: data.check_in_window_start || null,
          check_in_window_end: data.check_in_window_end || null,
          late_cancellation_rule: data.late_cancellation_rule || null,
        })
        .returning();

      // Log successful creation
      console.log(`✅ Schedule created successfully: ${scheduleResult.id} for class ${data.class_id}`);

      return scheduleResult;
    });
  }

  /**
   * Validate foreign key references
   *
   * Extracted untuk reusability dan cleaner code.
   */
  private static async validateForeignKeyReferences(data: any, tx?: any) {
    const dbInstance = tx || db;

    // Check if class exists and belongs to tenant
    const [classExists] = await dbInstance
      .select({ id: classes.id })
      .from(classes)
      .where(and(
        eq(classes.id, data.class_id),
        eq(classes.tenantId, data.tenant_id)
      ))
      .limit(1);

    if (!classExists) {
      throw new Error(`Class with ID ${data.class_id} not found or does not belong to tenant ${data.tenant_id}`);
    }

    // Check if location exists and belongs to tenant (if provided)
    if (data.location_id) {
      const [locationExists] = await dbInstance
        .select({ id: locations.id })
        .from(locations)
        .where(and(
          eq(locations.id, data.location_id),
          eq(locations.tenantId, data.tenant_id)
        ))
        .limit(1);

      if (!locationExists) {
        throw new Error(`Location with ID ${data.location_id} not found or does not belong to tenant ${data.tenant_id}`);
      }
    }

    // Check if facility exists and belongs to tenant (if provided)
    if (data.facility_id) {
      const [facilityExists] = await dbInstance
        .select({ id: facilities.id })
        .from(facilities)
        .where(and(
          eq(facilities.id, data.facility_id),
          eq(facilities.tenantId, data.tenant_id)
        ))
        .limit(1);

      if (!facilityExists) {
        throw new Error(`Facility with ID ${data.facility_id} not found or does not belong to tenant ${data.tenant_id}`);
      }
    }

    // Check if staff/instructor exists (if provided)
    if (data.staff_id) {
      const [staffExists] = await dbInstance
        .select({ id: users.id })
        .from(users)
        .where(eq(users.id, data.staff_id))
        .limit(1);

      if (!staffExists) {
        throw new Error(`Staff/Instructor with ID ${data.staff_id} not found`);
      }
    }
  }

  /**
   * Update class schedule
   *
   * Enhanced dengan comprehensive conflict detection untuk updates.
   * Includes atomic transactions dan validation untuk prevent conflicts.
   */
  static async update(id: string, data: {
    class_id?: string;
    location_id?: string;
    facility_id?: string;
    staff_id?: string;
    start_date?: string;
    end_date?: string;
    start_time?: Date;
    end_time?: Date;
    duration?: number;
    calender_color?: string;
    repeat_rule?: string;
    pax?: number;
    waitlist?: number;
    allow_classpass?: boolean;
    is_private?: boolean;
    publish_now?: boolean;
    publish_at?: Date;
    auto_cancel_if_minimum_not_met?: boolean;
    booking_window_start?: Date;
    booking_window_end?: Date;
    check_in_window_start?: Date;
    check_in_window_end?: Date;
    late_cancellation_rule?: string;
  }): Promise<ClassSchedule | null> {
    // FAANG-Level Atomic Transaction dengan Conflict Detection untuk Updates
    return await db.transaction(async (tx) => {
      // Step 1: Get existing schedule untuk comparison
      const existingSchedule = await this.getById(id);
      if (!existingSchedule) {
        throw new Error("Class schedule not found");
      }

      // Step 2: Determine what's changing untuk conflict detection
      const updatedStartTime = data.start_time || existingSchedule.start_time;
      const updatedEndTime = data.end_time || existingSchedule.end_time;
      const updatedLocationId = data.location_id !== undefined ? data.location_id : existingSchedule.location_id;
      const updatedFacilityId = data.facility_id !== undefined ? data.facility_id : existingSchedule.facility_id;
      const updatedStaffId = data.staff_id !== undefined ? data.staff_id : existingSchedule.staff_id;

      // Step 3: Comprehensive Conflict Detection (only if time/location/facility/staff changes)
      const hasTimeChange = data.start_time || data.end_time;
      const hasResourceChange = data.location_id !== undefined || data.facility_id !== undefined || data.staff_id !== undefined;

      if (hasTimeChange || hasResourceChange) {
        if (updatedStartTime && updatedEndTime) {
          const conflictParams: ConflictCheckParams = {
            tenantId: existingSchedule.tenant_id || 0,
            startTime: updatedStartTime,
            endTime: updatedEndTime,
            locationId: updatedLocationId || undefined,
            facilityId: updatedFacilityId || undefined,
            staffId: updatedStaffId || undefined,
            excludeScheduleId: id, // Exclude current schedule from conflict check
          };

          const conflictResult = await this.checkScheduleConflicts(conflictParams);

          if (!conflictResult.canProceed) {
            const blockingConflicts = conflictResult.conflicts.filter(c => c.severity === 'BLOCKING');
            const conflictMessages = blockingConflicts.map(conflict => {
              const details = conflict.conflictDetails;
              const resource = details.resourceConflict.location || details.resourceConflict.facility || 'staff';
              const timeStr = `${details.timeOverlap.start.toLocaleTimeString()} - ${details.timeOverlap.end.toLocaleTimeString()}`;
              return `${conflict.conflictType.replace('_', ' ').toLowerCase()} conflict with existing schedule at ${resource} during ${timeStr}`;
            });

            const errorMessage = `Schedule update conflict detected: ${conflictMessages.join('; ')}`;

            // Add suggestions if available
            if (conflictResult.suggestions && conflictResult.suggestions.length > 0) {
              const suggestionTexts = conflictResult.suggestions.slice(0, 3).map(s =>
                `${s.suggestedStartTime.toLocaleTimeString()} - ${s.suggestedEndTime.toLocaleTimeString()}`
              );
              throw new Error(`${errorMessage}. Suggested alternatives: ${suggestionTexts.join(', ')}`);
            }

            throw new Error(errorMessage);
          }

          // Log warnings if any
          if (conflictResult.warnings && conflictResult.warnings.length > 0) {
            console.warn("Schedule update warnings:", conflictResult.warnings);
          }
        }
      }

      // Step 4: Validate foreign key references if they're being updated
      if (data.class_id || data.location_id !== undefined || data.facility_id !== undefined || data.staff_id !== undefined) {
        const validationData = {
          tenant_id: existingSchedule.tenant_id,
          class_id: data.class_id || existingSchedule.class_id,
          location_id: updatedLocationId,
          facility_id: updatedFacilityId,
          staff_id: updatedStaffId,
        };
        await this.validateForeignKeyReferences(validationData, tx);
      }

      // Step 5: Perform the update
      const [scheduleResult] = await tx
        .update(class_schedules)
        .set({
          class_id: data.class_id,
          location_id: data.location_id,
          facility_id: data.facility_id,
          staff_id: data.staff_id,
          start_date: data.start_date,
          end_date: data.end_date,
          start_time: data.start_time,
          end_time: data.end_time,
          duration: data.duration,
          calender_color: data.calender_color,
          repeat_rule: data.repeat_rule,
          pax: data.pax,
          waitlist: data.waitlist,
          allow_classpass: data.allow_classpass,
          is_private: data.is_private,
          publish_now: data.publish_now,
          publish_at: data.publish_at,
          auto_cancel_if_minimum_not_met: data.auto_cancel_if_minimum_not_met,
          booking_window_start: data.booking_window_start,
          booking_window_end: data.booking_window_end,
          check_in_window_start: data.check_in_window_start,
          check_in_window_end: data.check_in_window_end,
          late_cancellation_rule: data.late_cancellation_rule,
        })
        .where(eq(class_schedules.id, id))
        .returning();

      // Log successful update
      console.log(`✅ Schedule updated successfully: ${id}`);

      return scheduleResult || null;
    });
  }

  /**
   * Delete class schedule
   *
   * Hapus class schedule berdasarkan ID.
   * Return class schedule yang dihapus untuk konfirmasi.
   */
  static async delete(id: string): Promise<ClassSchedule> {
    // First check if schedule exists
    const existingSchedule = await this.getById(id);
    if (!existingSchedule) {
      throw new Error("Class schedule not found");
    }

    // Delete the schedule and return the deleted record
    const [deletedSchedule] = await db
      .delete(class_schedules)
      .where(eq(class_schedules.id, id))
      .returning();

    if (!deletedSchedule) {
      throw new Error("Failed to delete class schedule");
    }

    return deletedSchedule;
  }

  /**
   * Get schedules by date range
   *
   * Ambil schedules dalam rentang tanggal tertentu.
   * Berguna untuk calendar view atau weekly/monthly schedule.
   */
  static async getByDateRange(
    tenantId: number,
    startDate: string,
    endDate: string
  ): Promise<ClassScheduleWithRelations[]> {
    const scheduleResults = await db
      .select({
        schedule: class_schedules,
        class_name: classes.name,
        location_name: locations.name,
        facility_name: facilities.name,
        instructor_name: users.name,
      })
      .from(class_schedules)
      .leftJoin(classes, eq(class_schedules.class_id, classes.id))
      .leftJoin(locations, eq(class_schedules.location_id, locations.id))
      .leftJoin(facilities, eq(class_schedules.facility_id, facilities.id))
      .leftJoin(users, eq(class_schedules.staff_id, users.id))
      .where(
        and(
          eq(class_schedules.tenant_id, tenantId),
          gte(class_schedules.start_date, startDate),
          lte(class_schedules.end_date, endDate)
        )
      )
      .orderBy(class_schedules.start_date, class_schedules.start_time);

    return scheduleResults.map(result => ({
      ...result.schedule,
      class_name: result.class_name || undefined,
      location_name: result.location_name || undefined,
      facility_name: result.facility_name || undefined,
      instructor_name: result.instructor_name || undefined,
    }));
  }
}
