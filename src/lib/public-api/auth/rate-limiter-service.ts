/**
 * Rate Limiter Service
 * 
 * FAANG-level rate limiting implementation with:
 * - Multiple rate limiting algorithms (Token Bucket, Sliding Window)
 * - Redis-based distributed rate limiting
 * - Per-API-key and per-IP rate limiting
 * - Graceful degradation and circuit breaker patterns
 * - Detailed metrics and monitoring
 */

import Redis from 'ioredis';
import { RateLimit, RateLimitStatus, RateLimitError } from '../auth-types';

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  keyPrefix: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface TokenBucketConfig {
  capacity: number;
  refillRate: number; // tokens per second
  refillPeriod: number; // milliseconds
}

export class RateLimiterService {
  private redis: Redis;
  private defaultConfig: RateLimitConfig;

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });

    this.defaultConfig = {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100,
      keyPrefix: 'rate_limit:',
    };
  }

  /**
   * Check rate limit for API key
   */
  async checkRateLimit(
    apiKeyId: string,
    rateLimit: RateLimit
  ): Promise<RateLimitStatus> {
    const now = Date.now();
    
    // Check multiple time windows
    const checks = await Promise.all([
      this.checkWindow(apiKeyId, 'minute', 60 * 1000, rateLimit.requestsPerMinute),
      this.checkWindow(apiKeyId, 'hour', 60 * 60 * 1000, rateLimit.requestsPerHour),
      this.checkWindow(apiKeyId, 'day', 24 * 60 * 60 * 1000, rateLimit.requestsPerDay),
      this.checkBurstLimit(apiKeyId, rateLimit.burstLimit),
    ]);

    // Find the most restrictive limit
    const mostRestrictive = checks.reduce((min, current) => 
      current.remaining < min.remaining ? current : min
    );

    // If any limit is exceeded, block the request
    const isBlocked = checks.some(check => check.remaining <= 0);

    if (isBlocked) {
      // Log rate limit hit
      await this.logRateLimitHit(apiKeyId, mostRestrictive);
      
      throw new RateLimitError(
        'Rate limit exceeded',
        Math.ceil((mostRestrictive.resetTime.getTime() - now) / 1000)
      );
    }

    // Increment counters for successful check
    await this.incrementCounters(apiKeyId);

    return mostRestrictive;
  }

  /**
   * Check rate limit for IP address
   */
  async checkIPRateLimit(
    ipAddress: string,
    config: RateLimitConfig = this.defaultConfig
  ): Promise<RateLimitStatus> {
    const key = `${config.keyPrefix}ip:${ipAddress}`;
    return this.slidingWindowRateLimit(key, config);
  }

  /**
   * Check rate limit for specific endpoint
   */
  async checkEndpointRateLimit(
    endpoint: string,
    identifier: string,
    config: RateLimitConfig
  ): Promise<RateLimitStatus> {
    const key = `${config.keyPrefix}endpoint:${endpoint}:${identifier}`;
    return this.slidingWindowRateLimit(key, config);
  }

  /**
   * Token bucket rate limiting implementation
   */
  async tokenBucketRateLimit(
    key: string,
    config: TokenBucketConfig
  ): Promise<RateLimitStatus> {
    const now = Date.now();
    const bucketKey = `token_bucket:${key}`;

    // Lua script for atomic token bucket operations
    const luaScript = `
      local bucket_key = KEYS[1]
      local capacity = tonumber(ARGV[1])
      local refill_rate = tonumber(ARGV[2])
      local refill_period = tonumber(ARGV[3])
      local now = tonumber(ARGV[4])
      local tokens_requested = tonumber(ARGV[5])

      local bucket = redis.call('HMGET', bucket_key, 'tokens', 'last_refill')
      local tokens = tonumber(bucket[1]) or capacity
      local last_refill = tonumber(bucket[2]) or now

      -- Calculate tokens to add based on time elapsed
      local time_elapsed = now - last_refill
      local tokens_to_add = math.floor(time_elapsed / refill_period * refill_rate)
      tokens = math.min(capacity, tokens + tokens_to_add)

      -- Check if we have enough tokens
      if tokens >= tokens_requested then
        tokens = tokens - tokens_requested
        redis.call('HMSET', bucket_key, 'tokens', tokens, 'last_refill', now)
        redis.call('EXPIRE', bucket_key, 3600) -- 1 hour TTL
        return {1, tokens, capacity}
      else
        redis.call('HMSET', bucket_key, 'tokens', tokens, 'last_refill', now)
        redis.call('EXPIRE', bucket_key, 3600)
        return {0, tokens, capacity}
      end
    `;

    const result = await this.redis.eval(
      luaScript,
      1,
      bucketKey,
      config.capacity.toString(),
      config.refillRate.toString(),
      config.refillPeriod.toString(),
      now.toString(),
      '1' // tokens requested
    ) as [number, number, number];

    const [allowed, remaining, limit] = result;
    const resetTime = new Date(now + config.refillPeriod);

    return {
      limit,
      remaining,
      resetTime,
    };
  }

  /**
   * Sliding window rate limiting implementation
   */
  async slidingWindowRateLimit(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitStatus> {
    const now = Date.now();
    const windowStart = now - config.windowMs;

    // Lua script for atomic sliding window operations
    const luaScript = `
      local key = KEYS[1]
      local window_start = tonumber(ARGV[1])
      local now = tonumber(ARGV[2])
      local max_requests = tonumber(ARGV[3])
      local window_ms = tonumber(ARGV[4])

      -- Remove expired entries
      redis.call('ZREMRANGEBYSCORE', key, 0, window_start)

      -- Count current requests in window
      local current_requests = redis.call('ZCARD', key)

      if current_requests < max_requests then
        -- Add current request
        redis.call('ZADD', key, now, now)
        redis.call('EXPIRE', key, math.ceil(window_ms / 1000))
        return {1, max_requests - current_requests - 1, max_requests}
      else
        return {0, 0, max_requests}
      end
    `;

    const result = await this.redis.eval(
      luaScript,
      1,
      key,
      windowStart.toString(),
      now.toString(),
      config.maxRequests.toString(),
      config.windowMs.toString()
    ) as [number, number, number];

    const [allowed, remaining, limit] = result;
    const resetTime = new Date(now + config.windowMs);

    return {
      limit,
      remaining,
      resetTime,
    };
  }

  /**
   * Check specific time window
   */
  private async checkWindow(
    apiKeyId: string,
    window: string,
    windowMs: number,
    maxRequests: number
  ): Promise<RateLimitStatus> {
    const key = `rate_limit:${apiKeyId}:${window}`;
    const config: RateLimitConfig = {
      windowMs,
      maxRequests,
      keyPrefix: '',
    };

    return this.slidingWindowRateLimit(key, config);
  }

  /**
   * Check burst limit using token bucket
   */
  private async checkBurstLimit(
    apiKeyId: string,
    burstLimit: number
  ): Promise<RateLimitStatus> {
    const key = `burst:${apiKeyId}`;
    const config: TokenBucketConfig = {
      capacity: burstLimit,
      refillRate: burstLimit / 60, // Refill over 1 minute
      refillPeriod: 1000, // 1 second
    };

    return this.tokenBucketRateLimit(key, config);
  }

  /**
   * Increment all rate limit counters
   */
  private async incrementCounters(apiKeyId: string): Promise<void> {
    const now = Date.now();
    
    // Increment counters for all windows
    const pipeline = this.redis.pipeline();
    
    ['minute', 'hour', 'day'].forEach(window => {
      const key = `rate_limit:${apiKeyId}:${window}`;
      pipeline.zadd(key, now, now);
    });

    await pipeline.exec();
  }

  /**
   * Log rate limit hit for monitoring
   */
  private async logRateLimitHit(
    apiKeyId: string,
    rateLimitStatus: RateLimitStatus
  ): Promise<void> {
    const logKey = `rate_limit_hits:${apiKeyId}`;
    const logData = {
      timestamp: Date.now(),
      limit: rateLimitStatus.limit,
      remaining: rateLimitStatus.remaining,
      resetTime: rateLimitStatus.resetTime.getTime(),
    };

    await this.redis.lpush(logKey, JSON.stringify(logData));
    await this.redis.ltrim(logKey, 0, 99); // Keep last 100 hits
    await this.redis.expire(logKey, 24 * 60 * 60); // 24 hours TTL
  }

  /**
   * Get rate limit statistics
   */
  async getRateLimitStats(apiKeyId: string): Promise<{
    currentLimits: {
      minute: RateLimitStatus;
      hour: RateLimitStatus;
      day: RateLimitStatus;
    };
    recentHits: Array<{
      timestamp: number;
      limit: number;
      remaining: number;
    }>;
  }> {
    // Get current limits
    const [minute, hour, day] = await Promise.all([
      this.checkWindow(apiKeyId, 'minute', 60 * 1000, 0), // 0 to just check, not increment
      this.checkWindow(apiKeyId, 'hour', 60 * 60 * 1000, 0),
      this.checkWindow(apiKeyId, 'day', 24 * 60 * 60 * 1000, 0),
    ]);

    // Get recent hits
    const hitsKey = `rate_limit_hits:${apiKeyId}`;
    const recentHitsData = await this.redis.lrange(hitsKey, 0, 9); // Last 10 hits
    const recentHits = recentHitsData.map(data => JSON.parse(data));

    return {
      currentLimits: { minute, hour, day },
      recentHits,
    };
  }

  /**
   * Reset rate limits for API key (admin function)
   */
  async resetRateLimits(apiKeyId: string): Promise<void> {
    const keys = [
      `rate_limit:${apiKeyId}:minute`,
      `rate_limit:${apiKeyId}:hour`,
      `rate_limit:${apiKeyId}:day`,
      `burst:${apiKeyId}`,
      `rate_limit_hits:${apiKeyId}`,
    ];

    await this.redis.del(...keys);
  }

  /**
   * Health check for Redis connection
   */
  async healthCheck(): Promise<{ status: 'pass' | 'fail'; responseTime?: number; error?: string }> {
    const start = Date.now();
    
    try {
      await this.redis.ping();
      const responseTime = Date.now() - start;
      
      return {
        status: 'pass',
        responseTime,
      };
    } catch (error) {
      return {
        status: 'fail',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    await this.redis.quit();
  }
}
