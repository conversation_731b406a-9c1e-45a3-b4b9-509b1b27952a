{"name": "starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "echo \"Tests will be implemented later\" && exit 0", "test:watch": "echo \"Tests will be implemented later\" && exit 0", "test:coverage": "echo \"Tests will be implemented later\" && exit 0", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:seed": "tsx src/lib/db/seed.ts", "db:seed:rbac": "tsx src/lib/db/seed-rbac.ts", "email:dev": "email dev --dir src/components/email-templates --port 3001", "email:export": "email export --dir src/components/email-templates --outDir out", "postinstall": "npm run db:generate"}, "dependencies": {"@auth/core": "^0.40.0", "@auth/drizzle-adapter": "^1.10.0", "@cloudflare/workers-types": "^4.20250620.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.1.1", "@react-email/render": "^1.1.3", "@simplewebauthn/browser": "^9.0.1", "@simplewebauthn/server": "^9.0.3", "@stripe/stripe-js": "^7.4.0", "@tanstack/query-devtools": "^5.81.2", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "@tanstack/react-router": "^1.121.41", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.11", "@tanstack/zod-form-adapter": "^0.42.1", "@types/bcryptjs": "^2.4.6", "@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.2", "drizzle-orm": "^0.44.2", "framer-motion": "^12.19.2", "jose": "^6.0.11", "lucide-react": "^0.523.0", "nanoid": "^5.1.5", "next": "15.3.4", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "pg": "^8.16.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-email": "^4.0.17", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.58.1", "resend": "^4.6.0", "sonner": "^2.0.5", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "tsx": "^4.20.3", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}